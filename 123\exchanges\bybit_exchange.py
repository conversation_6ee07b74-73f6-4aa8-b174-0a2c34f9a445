"""
Bybit交易所接口实现
支持统一账户模式
"""

import asyncio
import aiohttp
import hmac
import hashlib
import time
import json
from typing import Dict, List, Optional, Any
import logging
from decimal import Decimal, InvalidOperation
import re
import os

from .exchanges_base import BaseExchange, OrderType, OrderSide, OrderStatus, AccountType
# 🔥 删除重复导入：get_exchange_symbol现在通过currency_adapter实例调用

# 🔥 统一日志系统 - 使用标准logger获取方式
from utils.logger import get_logger
logger = get_logger(__name__)


class BybitExchange(BaseExchange):
    """Bybit交易所实现"""

    def __init__(self, api_key: str, api_secret: str, **kwargs):
        """初始化Bybit交易所"""
        super().__init__("bybit", api_key, api_secret, **kwargs)
        
        # 🔥 添加logger初始化
        self.logger = logging.getLogger(f"bybit_exchange")
        
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://api.bybit.com"
        
        # 🔥 根源修复：统一限速配置，确保三交易所一致性
        if not hasattr(self, 'rate_limit') or self.rate_limit > 4:
            self.rate_limit = 4  # 🔥 根源修复：降低到4次/秒，确保30+代币健壮启动
            logger.info(f"🔧 Bybit API限制根源修复为{self.rate_limit}次/秒，确保30+代币健壮启动")

        # 初始化统一模块
        logger.info(f"初始化Bybit交易所接口，API请求限制: {self.rate_limit}/秒")
        
        # 🔥 统一模块初始化 - 避免重复逻辑
        from core.trading_rules_preloader import get_trading_rules_preloader
        from core.universal_token_system import get_universal_token_system  
        from exchanges.currency_adapter import CurrencyAdapter
        from core.unified_opening_manager import get_opening_manager
        from core.unified_closing_manager import get_closing_manager
        
        self.rules_preloader = get_trading_rules_preloader()
        self.token_system = get_universal_token_system()
        self.currency_adapter = CurrencyAdapter()
        self.opening_manager = get_opening_manager()
        self.closing_manager = get_closing_manager()
        
        logger.info("✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑")

        # 🔥 最简化统一初始化 - 一行代码完成所有模块和配置设置
        from exchanges.unified_exchange_initializer import setup_exchange_unified
        setup_exchange_unified("Bybit", self)

        # 🔥 统一会话管理 - 使用第19个核心统一模块
        from core.unified_http_session_manager import get_unified_session_manager
        self.session_manager = get_unified_session_manager()
        self.session: Optional[aiohttp.ClientSession] = None

        # Bybit使用统一账户
        self.unified_account = True

        # 服务器时间偏移(ms)，用于修正本地时间与服务器时间的差异
        self.time_offset = 0
        # 是否已同步时间
        self.time_synced = False

        logger.debug(f"Bybit交易所初始化完成: 杠杆={self.default_leverage}x, 最小价值={self.min_order_amount_usd}USDT")

    async def _ensure_session(self):
        """确保会话存在 - 🔥 使用统一会话管理器"""
        if self.session is None or self.session.closed:
            self.session = await self.session_manager.get_session("bybit")

    async def close(self):
        """关闭会话 - 🔥 使用统一会话管理器"""
        try:
            await self.session_manager.close_session("bybit")
            self.session = None
        except Exception as e:
            self.logger.error(f"关闭Bybit会话失败: {e}")

    async def _rate_limit(self):
        """🔥 统一限速方法 - 与API调用优化器保持一致"""
        # 使用API调用优化器的统一限速控制
        from core.api_call_optimizer import get_api_optimizer
        optimizer = get_api_optimizer()
        await optimizer._rate_limit_wait("bybit")

    async def _rate_limit_with_optimizer(self):
        """🔥 新增：使用API调用优化器的精确限速控制"""
        # 与OKX保持一致的限速方法
        await self._rate_limit()

    async def _sync_time(self):
        """🔥 修复：同步服务器时间 - 使用统一的时间戳处理逻辑"""
        try:
            await self._ensure_session()
            response = await self.session.get(f"{self.base_url}/v5/market/time")
            data = await response.json()

            if data["retCode"] == 0 and "result" in data:
                result = data["result"]
                server_time = None

                # 🔥 优先使用timeNano（纳秒级，最精确）
                if "timeNano" in result:
                    try:
                        server_time = int(result["timeNano"]) // 1000000
                        logger.debug(f"Bybit _sync_time使用timeNano: {result['timeNano']} -> {server_time}ms")
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Bybit _sync_time timeNano转换失败: {result['timeNano']}, 错误: {e}")

                # 🔥 备用方案：使用timeSecond（秒级）
                if server_time is None and "timeSecond" in result:
                    try:
                        server_time = int(result["timeSecond"]) * 1000
                        logger.debug(f"Bybit _sync_time使用timeSecond: {result['timeSecond']} -> {server_time}ms")
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Bybit _sync_time timeSecond转换失败: {result['timeSecond']}, 错误: {e}")

                # 🔥 第三备用方案：使用顶级time字段
                if server_time is None and "time" in data:
                    try:
                        server_time = int(data["time"])
                        logger.debug(f"Bybit _sync_time使用顶级time字段: {data['time']}ms")
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Bybit _sync_time顶级time字段转换失败: {data['time']}, 错误: {e}")

                if server_time is not None:
                    local_time = int(time.time() * 1000)
                    self.time_offset = server_time - local_time
                    self.time_synced = True
                    logger.info(f"Bybit服务器时间同步成功，本地时间: {local_time}, 服务器时间: {server_time}, 偏移: {self.time_offset}ms")
                    return True
                else:
                    logger.error(f"Bybit同步时间失败: 无法解析时间字段，result: {result}")
                    return False
            else:
                logger.error(f"Bybit同步时间失败: {data}")
                return False
        except Exception as e:
            logger.error(f"Bybit同步时间异常: {e}")
            return False

    def _get_timestamp(self):
        """获取同步后的时间戳（按照官方SDK标准）"""
        # 官方SDK标准：int(time.time() * 10**3)
        local_time = int(time.time() * 1000)
        if self.time_synced:
            # 使用同步后的时间，并保留一些缓冲以确保在recv_window内
            adjusted_time = local_time + self.time_offset
            logger.debug(f"时间戳计算: 本地时间={local_time}, 偏移={self.time_offset}, 调整后={adjusted_time}")
            return str(adjusted_time)
        return str(local_time)

    def _safe_float_convert(self, value):
        """安全地将值转换为浮点数"""
        if value is None or value == '' or value == 'null':
            return 0.0

        try:
            # 使用Decimal进行精确转换，避免浮点数精度问题
            return float(Decimal(str(value)))
        except (ValueError, TypeError, InvalidOperation) as e:
            logger.warning(f"无法将值转换为浮点数: {value}, 错误: {e}")
            return 0.0

    def _safe_parse_response(self, data):
        """
        安全地解析API响应，处理可能的数值转换
        """
        if data is None:
            return None

        # 需要转换为浮点数的字段名正则模式
        float_patterns = [
            r'price', r'amount', r'quantity', r'volume', r'size',
            r'value', r'balance', r'fee', r'cost', r'rate'
        ]

        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                # 递归处理嵌套的字典
                if isinstance(value, (dict, list)):
                    result[key] = self._safe_parse_response(value)
                # 检查是否需要转换为浮点数
                elif any(re.search(pattern, key.lower()) for pattern in float_patterns):
                    result[key] = self._safe_float_convert(value)
                else:
                    result[key] = value
            return result

        elif isinstance(data, list):
            # 递归处理列表中的每个元素
            return [self._safe_parse_response(item) for item in data]

        return data

    def _clean_numeric_data(self, data):
        """清理数据中的数值字段"""
        if isinstance(data, dict):
            cleaned = {}
            for key, value in data.items():
                if isinstance(value, str) and self._is_numeric_field(key):
                    cleaned[key] = self._safe_float_convert(value)
                elif isinstance(value, (dict, list)):
                    cleaned[key] = self._clean_numeric_data(value)
                else:
                    cleaned[key] = value
            return cleaned
        elif isinstance(data, list):
            return [self._clean_numeric_data(item) for item in data]
        else:
            return data

    def _is_numeric_field(self, field_name):
        """判断字段是否应该是数值类型"""
        numeric_fields = {
            'price', 'qty', 'volume', 'amount', 'balance', 'available',
            'locked', 'bid1Price', 'ask1Price', 'lastPrice', 'prevPrice24h',
            'price24hPcnt', 'highPrice24h', 'lowPrice24h', 'volume24h',
            'turnover24h', 'usdIndexPrice', 'markPrice', 'openInterest',
            'totalVolume', 'totalTurnover', 'size', 'value'
        }
        return any(field in field_name.lower() for field in numeric_fields)

    def _generate_signature(self, timestamp: str, method: str, path_with_query: str, body: Dict = None) -> str:
        """
        生成Bybit API签名（V5 API标准）
        参考官方文档: https://bybit-exchange.github.io/docs/v5/guide/auth

        签名字符串格式:
        - GET请求: timestamp + api_key + recv_window + queryString
        - POST请求: timestamp + api_key + recv_window + jsonBodyString
        """
        recv_window = "5000"  # 官方推荐的接收窗口

        # 🔥 关键修复：确保所有参数都是字符串，防止None拼接错误
        if timestamp is None:
            logger.error("timestamp为None，无法生成签名")
            raise ValueError("timestamp不能为None")
        
        if self.api_key is None:
            logger.error("api_key为None，无法生成签名")
            raise ValueError("api_key不能为None")
        
        # 确保都是字符串类型
        timestamp = str(timestamp)
        api_key = str(self.api_key)
        recv_window = str(recv_window)

        # 构建参数字符串
        if method == "GET":
            # GET请求：提取查询参数
            if "?" in path_with_query:
                query_string = path_with_query.split("?", 1)[1]
            else:
                query_string = ""
            param_str = str(query_string)
        else:
            # POST请求：使用JSON字符串（严格按照官方SDK要求格式化）
            if body:
                # 关键修复：使用与官方SDK完全相同的JSON格式化方式
                # 官方SDK代码：return json.dumps(parameters)
                # 这会在冒号后保留空格，例如: {"category": "linear", "symbol": "BTCUSDT"}
                param_str = json.dumps(body)

                # 详细调试：显示JSON格式化结果
                logger.debug(f"POST请求JSON参数字符串: {param_str}")
            else:
                param_str = ""

        # 🔥 关键修复：确保param_str也是字符串
        param_str = str(param_str)

        # 构建签名字符串：timestamp + api_key + recv_window + 参数字符串
        # 关键修复：确保API key正确拼接
        sign_str = timestamp + api_key + recv_window + param_str

        # 详细调试日志
        logger.debug(f"Bybit签名构建:")
        logger.debug(f"  timestamp: {timestamp}")
        logger.debug(f"  api_key: {api_key[:8]}***")
        logger.debug(f"  recv_window: {recv_window}")
        logger.debug(f"  param_str: {param_str}")
        logger.debug(f"  完整签名字符串长度: {len(sign_str)}")

        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            sign_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        logger.debug(f"  生成的签名: {signature[:16]}...")

        return signature

    def _apply_bybit_parameter_fixes(self, query: Dict) -> Dict:
        """
        🔥 应用Bybit参数修复 - 根据官方SDK防止认证签名错误

        参考pybit官方SDK的关键修复：
        1. "Bug fix: change floating whole numbers to integers to prevent auth signature errors"
        2. "Fixed trailing decimal zero to prevent auth signature errors"
        """
        try:
            # 创建副本避免修改原始数据
            fixed_query = {}

            for key, value in query.items():
                if value is None:
                    # Remove params with None value from the request
                    continue
                elif isinstance(value, float):
                    # 🔥 关键修复1：浮点整数转换为整数
                    if value == int(value):
                        fixed_query[key] = int(value)
                    else:
                        # 🔥 关键修复2：处理浮点数的尾随零
                        # 确保没有尾随零导致签名错误
                        value_str = f"{value:.10f}".rstrip('0').rstrip('.')
                        if not value_str or value_str == '':
                            value_str = "0"
                        # 尝试转换回数字类型
                        try:
                            if '.' in value_str:
                                fixed_query[key] = float(value_str)
                            else:
                                fixed_query[key] = int(value_str)
                        except ValueError:
                            fixed_query[key] = value_str
                elif isinstance(value, str):
                    # 🔥 关键修复3：字符串数字的尾随零处理
                    if self._is_numeric_string(value):
                        try:
                            num_value = float(value)
                            if num_value == int(num_value):
                                fixed_query[key] = int(num_value)
                            else:
                                # 去除尾随零
                                cleaned = value.rstrip('0').rstrip('.')
                                if not cleaned or cleaned == '':
                                    cleaned = "0"
                                fixed_query[key] = cleaned
                        except ValueError:
                            fixed_query[key] = value
                    else:
                        fixed_query[key] = value
                else:
                    fixed_query[key] = value

            self.logger.debug(f"🔧 Bybit参数修复: {len(query)} → {len(fixed_query)} 参数")
            return fixed_query

        except Exception as e:
            self.logger.error(f"❌ Bybit参数修复失败: {e}")
            # 发生错误时返回原始查询（去除None值）
            return {k: v for k, v in query.items() if v is not None}

    def _is_numeric_string(self, value: str) -> bool:
        """检查字符串是否为数字"""
        try:
            float(value)
            return True
        except ValueError:
            return False

    def _apply_final_trailing_zero_fix(self, query: Dict) -> Dict:
        """
        🔥 最终尾随零修复 - 在string_fields转换后应用

        这是解决Bybit认证签名错误的关键步骤：
        Python的str(1.0)会产生'1.0'，必须在最后清理尾随零
        """
        try:
            fixed_query = {}

            for key, value in query.items():
                if isinstance(value, str) and self._is_numeric_string(value):
                    # 对数字字符串应用尾随零修复
                    if '.' in value:
                        # 去除尾随零
                        fixed_value = value.rstrip('0')
                        # 如果小数点后没有数字，去除小数点
                        if fixed_value.endswith('.'):
                            fixed_value = fixed_value[:-1]
                        # 确保不是空字符串
                        if not fixed_value or fixed_value == '':
                            fixed_value = "0"
                        fixed_query[key] = fixed_value
                    else:
                        fixed_query[key] = value
                else:
                    fixed_query[key] = value

            # 记录修复过程
            changes = []
            for key in query:
                if key in fixed_query and str(query[key]) != str(fixed_query[key]):
                    changes.append(f"{key}: '{query[key]}' → '{fixed_query[key]}'")

            if changes:
                self.logger.debug(f"🔧 最终尾随零修复: {', '.join(changes)}")

            return fixed_query

        except Exception as e:
            self.logger.error(f"❌ 最终尾随零修复失败: {e}")
            return query  # 返回原始查询

    def _apply_official_sdk_fixes(self, query: Dict) -> Dict:
        """
        🔥 按照Bybit官方SDK标准处理参数 - 只做必要的浮点数修复
        参考官方SDK _http_manager.py 第200-205行和prepare_payload方法
        """
        try:
            fixed_query = {}

            # 🔥 官方SDK标准的参数类型转换
            string_params = ["qty", "price", "triggerPrice", "takeProfit", "stopLoss", "buyLeverage", "sellLeverage"]
            integer_params = ["positionIdx"]

            for key, value in query.items():
                if value is None:
                    continue  # 移除None值

                # 🔥 官方SDK标准：特定参数的字符串转换
                if key in string_params:
                    if not isinstance(value, str):
                        # 🔥 修复：浮点数整数化后再转字符串
                        if isinstance(value, float) and value == int(value):
                            fixed_query[key] = str(int(value))
                        else:
                            fixed_query[key] = str(value)
                    else:
                        fixed_query[key] = value
                # 🔥 官方SDK标准：特定参数的整数转换
                elif key in integer_params:
                    if not isinstance(value, int):
                        fixed_query[key] = int(value)
                    else:
                        fixed_query[key] = value
                # 🔥 官方SDK标准：浮点数整数化修复
                elif isinstance(value, float) and value == int(value):
                    fixed_query[key] = int(value)
                else:
                    fixed_query[key] = value

            self.logger.debug(f"🔧 官方SDK标准参数处理: {len(query)} → {len(fixed_query)} 参数")
            return fixed_query

        except Exception as e:
            self.logger.error(f"❌ 官方SDK参数处理失败: {e}")
            # 发生错误时返回原始查询（去除None值）
            return {k: v for k, v in query.items() if v is not None}

    async def _request(self, method: str, endpoint: str, params: Dict = None,
                          data: Dict = None, signed: bool = True) -> Dict:
        """发送请求到Bybit API - 按照官方SDK标准实现"""
        await self._ensure_session()
        await self._rate_limit()

        # 统一参数处理：官方SDK对GET和POST都使用query参数
        query = params if params else data if data else {}

        # 🔥 修复关键问题：杠杆设置API按照官方SDK标准处理，避免10001错误
        if query and endpoint != "/v5/position/set-leverage":
            query = self._apply_bybit_parameter_fixes(query)
        elif endpoint == "/v5/position/set-leverage":
            # 🔥 杠杆设置API：按照官方SDK标准，只做基础的浮点数修复
            query = self._apply_official_sdk_fixes(query)
            logger.debug(f"🔧 杠杆设置API使用官方SDK标准处理: {query}")

        # 🔥 修复：只有签名请求才需要时间戳
        fixed_timestamp = None
        if signed:
            # 每次签名请求前都强制同步时间，但只获取一次固定时间戳
            sync_success = await self._sync_time()
            if not sync_success:
                logger.warning("Bybit时间同步失败，使用本地时间")
                self.time_synced = False

            # 获取一次固定时间戳，在整个请求过程中保持不变
            fixed_timestamp = self._get_timestamp()
            logger.debug(f"Bybit固定时间戳: {fixed_timestamp}")

        try:
            # 构建URL
            url = f"{self.base_url}{endpoint}"

            # 设置HTTP头
            headers = {
                "Content-Type": "application/json"
            }

            # 🔥 修复：无论是否签名，都需要构建查询字符串
            if method == "GET" and query:
                query_string = "&".join([
                    str(k) + "=" + str(v)
                    for k, v in sorted(query.items())
                    if v is not None
                ])
                url = f"{self.base_url}{endpoint}?{query_string}"

            # 如果是已签名请求，添加验证信息
            if signed:
                # 使用环境变量配置的接收窗口
                recv_window = self.recv_window

                # 添加认证头（使用固定时间戳）- 按照官方SDK标准
                headers["X-BAPI-API-KEY"] = self.api_key
                headers["X-BAPI-TIMESTAMP"] = fixed_timestamp
                headers["X-BAPI-RECV-WINDOW"] = recv_window
                headers["X-BAPI-SIGN-TYPE"] = "2"  # 官方SDK标准，这个头部很重要！

                # 按照官方SDK的prepare_payload方式准备参数
                if method == "GET":
                    # GET请求：使用已构建的URL
                    if query:
                        query_string = "&".join([
                            str(k) + "=" + str(v)
                            for k, v in sorted(query.items())
                            if v is not None
                        ])
                        full_path = f"{endpoint}?{query_string}"
                        req_params = query_string
                    else:
                        full_path = endpoint
                        req_params = ""
                else:
                    # POST请求：按照官方SDK的cast_values处理参数
                    processed_query = query.copy() if query else {}

                    if processed_query:
                        # 按照官方SDK的参数类型转换
                        string_fields = ["qty", "price", "triggerPrice", "takeProfit", "stopLoss"]
                        integer_fields = ["positionIdx"]

                        for key, value in processed_query.items():
                            if key in string_fields:
                                if type(value) != str:
                                    processed_query[key] = str(value)
                            elif key in integer_fields:
                                if type(value) != int:
                                    try:
                                        processed_query[key] = int(value)
                                    except (ValueError, TypeError):
                                        pass

                        # 🔥 关键修复：在字符串转换后再次应用尾随零修复
                        # 这是解决Bybit认证签名错误的最后一步
                        processed_query = self._apply_final_trailing_zero_fix(processed_query)

                        # 转换为JSON字符串（用于HTTP请求体）
                        req_params = json.dumps(processed_query)
                    else:
                        req_params = ""
                        processed_query = {}
                    full_path = endpoint

                # 生成签名（使用固定时间戳）
                if method == "GET":
                    signature = self._generate_signature(fixed_timestamp, method, full_path, None)
                else:
                    # POST请求：传递处理后的字典给签名方法，让签名方法内部进行JSON序列化
                    signature = self._generate_signature(fixed_timestamp, method, full_path, processed_query)
                headers["X-BAPI-SIGN"] = signature

            # 发送请求
            logger.debug(f"Bybit请求: {method} {url}")
            logger.debug(f"Bybit请求头: {headers}")
            logger.debug(f"Bybit请求参数: {query}")

            if method == "GET":
                async with self.session.get(url, headers=headers) as response:
                    result = await response.json()
            else:
                # POST请求 - 按照官方SDK发送data参数
                post_data = req_params if signed else (json.dumps(query) if query else "")
                async with self.session.post(url, headers=headers, data=post_data) as response:
                    result = await response.json()

            # 检查返回码
            if result.get("retCode") != 0:
                self.error_count += 1
                error_msg = f"{result.get('retCode')}: {result.get('retMsg', '未知错误')}"

                # 🔥 修复：110043错误是正常的杠杆状态，不应记录为ERROR
                if "110043" in error_msg or "leverage not modified" in error_msg.lower():
                    logger.info(f"✅ Bybit杠杆状态正常: {error_msg}")
                    # 杠杆状态正常，不抛出异常
                    return self._safe_parse_response(result.get("result"))
                # 🔥 修复：10001错误是正常的交易对不存在，不应记录为ERROR
                elif "10001" in error_msg and ("symbol invalid" in error_msg.lower() or "not supported symbols" in error_msg.lower()):
                    logger.debug(f"🔍 Bybit交易对不存在: {error_msg} - 这是正常情况")
                    # 🔥 关键修复：交易对不存在时抛出特定异常，让上层优雅处理
                    raise ValueError(f"交易对不存在: {error_msg}")
                else:
                    logger.error(f"Bybit API错误: {error_msg}")

                # 如果是时间戳错误，添加详细信息便于调试
                if "timestamp" in error_msg.lower() or "recv_window" in error_msg.lower():
                    logger.error(f"时间戳错误详情: 使用时间戳={fixed_timestamp}, 错误={error_msg}")

                raise Exception(f"Bybit API错误: {error_msg}")

            # 安全解析响应数据
            return self._safe_parse_response(result.get("result"))

        except ValueError as ve:
            # 🔥 修复：交易对不存在等特定错误不增加错误计数，直接重新抛出
            if "交易对不存在" in str(ve):
                logger.debug(f"🔍 Bybit交易对不存在（正常情况）: {ve}")
            else:
                logger.error(f"Bybit参数错误: {ve}")
                self.error_count += 1
            raise
        except Exception as e:
            self.error_count += 1
            logger.error(f"Bybit请求失败: {e}")
            raise

    async def get_balance(self, account_type: AccountType = AccountType.SPOT) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            # 🔥 关键修复：Bybit现在只支持UNIFIED账户类型，强制使用UNIFIED
            # 无论传入什么账户类型，都使用UNIFIED，因为Bybit已弃用其他账户类型
            category = "UNIFIED"
            
            logger.info(f"Bybit查询余额: 原始account_type={account_type.value}, 强制使用category={category}")

            response = await self._request(
                "GET", "/v5/account/wallet-balance",
                params={"accountType": category},
                signed=True
            )

            balances = {}
            
            logger.debug(f"Bybit原始余额响应: {response}")

            if not isinstance(response, dict) and not isinstance(response, list):
                logger.warning(f"Bybit余额响应格式不正确: {type(response)}")
                return balances

            # 处理响应数据
            account_list = []
            if isinstance(response, list):
                account_list = response
            elif isinstance(response, dict) and "list" in response:
                account_list = response["list"]
            else:
                logger.warning(f"Bybit余额响应格式异常: {response}")
                return balances

            if not account_list:
                logger.warning("Bybit余额响应中没有账户列表")
                return balances

            # 🔥 关键修复：根据API文档，获取totalAvailableBalance用于期货保证金计算
            total_available_balance_usd = 0
            if account_list and len(account_list) > 0:
                account = account_list[0]
                total_available_balance_str = account.get("totalAvailableBalance", "0")
                total_available_balance_usd = self._safe_float_convert(total_available_balance_str)
                logger.info(f"Bybit UNIFIED账户总可用余额: ${total_available_balance_usd:.2f} USD")

            # 处理每个账户的币种余额
            for account in account_list:
                coin_list = account.get("coin", [])
                if not coin_list:
                    continue

                for coin in coin_list:
                    try:
                        currency = coin.get("coin", "")
                        if not currency:
                            continue

                        wallet_balance = self._safe_float_convert(coin.get("walletBalance", 0))
                        locked_balance = self._safe_float_convert(coin.get("locked", 0))
                        
                        # 🔥 关键修复：根据官方API文档处理UNIFIED账户的余额
                        # 因为所有账户都是UNIFIED，统一处理
                        # UNIFIED账户：availableToWithdraw已弃用，使用walletBalance - locked
                        available_balance = max(0, wallet_balance - locked_balance)
                        
                        # 🔥 对于USDT，还需要考虑期货保证金占用
                        if currency == "USDT":
                            # 获取期货占用的保证金
                            total_order_im = self._safe_float_convert(coin.get("totalOrderIM", 0))
                            total_position_im = self._safe_float_convert(coin.get("totalPositionIM", 0))
                            # 实际可用 = 钱包余额 - 锁定余额 - 订单保证金 - 持仓保证金
                            futures_margin_used = total_order_im + total_position_im
                            available_balance = max(0, wallet_balance - locked_balance - futures_margin_used)
                            
                            logger.info(f"Bybit USDT余额详细计算: "
                                      f"钱包余额={wallet_balance:.2f}, "
                                      f"锁定={locked_balance:.2f}, "
                                      f"订单保证金={total_order_im:.2f}, "
                                      f"持仓保证金={total_position_im:.2f}, "
                                      f"可用余额={available_balance:.2f}")
                        
                        logger.debug(f"Bybit UNIFIED {currency}: 钱包={wallet_balance:.6f}, 锁定={locked_balance:.6f}, 可用={available_balance:.6f}")

                        # 只记录有余额的币种
                        if available_balance > 0 or wallet_balance > 0:
                            balances[currency] = {
                                'available': available_balance,
                                'locked': locked_balance,
                                'wallet_balance': wallet_balance  # 🔥 新增：保存原始钱包余额供调试使用
                            }
                            
                            logger.debug(f"Bybit {currency} 最终余额: 可用={available_balance:.6f}, 锁定={locked_balance:.6f}")

                    except Exception as coin_err:
                        logger.error(f"处理Bybit币种数据时出错: {coin_err}, 币种数据: {coin}")
                        continue

            logger.info(f"Bybit账户余额处理完成: {len(balances)}个币种")

            # 🔥 调试信息：如果USDT余额为0，记录详细信息
            if balances.get("USDT", {}).get("available", 0) == 0:
                logger.warning(f"Bybit USDT可用余额为0！原始响应摘要:")
                for account in account_list:
                    usdt_coins = [coin for coin in account.get("coin", []) if coin.get("coin") == "USDT"]
                    if usdt_coins:
                        usdt_coin = usdt_coins[0]
                        logger.warning(f"  USDT数据: walletBalance={usdt_coin.get('walletBalance')}, "
                                     f"locked={usdt_coin.get('locked')}, "
                                     f"totalOrderIM={usdt_coin.get('totalOrderIM')}, "
                                     f"totalPositionIM={usdt_coin.get('totalPositionIM')}")

            return balances

        except Exception as e:
            logger.error(f"获取Bybit余额失败: {e}")
            # 🔥 修复：返回格式与其他交易所保持一致
            return {"USDT": {'available': 0.0, 'locked': 0.0}}

    # 🔥 删除RestAPI价格获取方法 - 统一使用WebSocket+缓存
    # async def get_ticker() 方法已删除，改为使用 websocket_price_cache.py
    # 所有价格数据通过WebSocket实时获取，避免RestAPI延迟

    async def get_orderbook(self, symbol: str, market_type: str = "spot", limit: int = 10) -> Dict[str, Any]:
        """
        🚨 严禁REST API - 只能使用WebSocket数据源

        Args:
            symbol: 交易对
            market_type: 市场类型
            limit: 深度限制

        Returns:
            空订单簿（强制使用WebSocket）
        """
        # 🚨 严禁REST API调用！订单簿数据只能来自WebSocket！
        self.logger.warning(f"🚨 get_orderbook已禁用！订单簿数据只能使用WebSocket！")
        self.logger.warning(f"   请使用: OpportunityScanner.market_data['{self.name.lower()}_{market_type}_{symbol}']")

        # 🔥 返回空订单簿，强制使用WebSocket数据流
        return {
            'asks': [],
            'bids': [],
            'timestamp': int(time.time() * 1000),
            'symbol': symbol,
            'exchange': 'bybit',
            'error': 'REST_API_DISABLED_USE_WEBSOCKET'
        }

    async def create_spot_order(self, symbol: str, side, order_type,
                               amount: float, price: Optional[float] = None,
                               params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        🔥 关键修复：直接现货下单方法，强制使用市价单
        专门为SpotTrader提供的底层API调用
        """
        try:
            # 处理参数类型
            if hasattr(side, 'value'):
                side_str = side.value
            else:
                side_str = str(side).lower()

            # 🔥 强制使用市价单，禁止限价单
            order_type_str = "market"
            logger.info(f"🔥 Bybit现货强制使用市价单: {symbol} {side_str}")

            # 转换交易对格式
            bybit_symbol = self.currency_adapter.get_exchange_symbol(symbol, "bybit", "spot")

            # 🔥 使用统一精度处理修复小数位过多问题
            # 防御性检查：确保trading_rules_preloader属性存在
            if not hasattr(self, 'trading_rules_preloader') or self.trading_rules_preloader is None:
                logger.warning("⚠️ trading_rules_preloader不存在，重新初始化统一模块")
                from exchanges.unified_exchange_initializer import setup_exchange_unified
                setup_exchange_unified("Bybit", self)
            
            formatted_amount = self.trading_rules_preloader.format_amount_unified(
                amount, "bybit", symbol, "spot"
            )
            logger.info(f"🔧 Bybit现货精度处理: {amount} -> {formatted_amount}")

            # 🚨 关键修复：防御性精度验证，确保不会出现"Order quantity has too many decimals"错误
            try:
                # 验证格式化后的数量是否符合Bybit API要求
                formatted_float = float(formatted_amount)
                formatted_str = str(formatted_float)

                # 检查小数位数是否过多
                if '.' in formatted_str:
                    decimal_places = len(formatted_str.split('.')[1])
                    if decimal_places > 6:  # Bybit通常支持最多6位小数
                        # 强制截取到6位小数
                        from decimal import Decimal, ROUND_DOWN
                        amount_decimal = Decimal(formatted_amount)
                        truncated = amount_decimal.quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
                        formatted_amount = str(truncated)
                        logger.warning(f"⚠️ Bybit精度防御性截取: {formatted_float} -> {formatted_amount}")

                logger.debug(f"✅ Bybit精度验证通过: {formatted_amount}")

            except Exception as precision_error:
                logger.error(f"❌ Bybit精度验证失败: {precision_error}")
                # 兜底处理：使用6位小数截取
                from decimal import Decimal, ROUND_DOWN
                try:
                    amount_decimal = Decimal(str(amount))
                    truncated = amount_decimal.quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
                    formatted_amount = str(truncated)
                    logger.warning(f"⚠️ Bybit精度兜底处理: {amount} -> {formatted_amount}")
                except:
                    formatted_amount = f"{amount:.6f}".rstrip('0').rstrip('.')
                    logger.error(f"❌ Bybit精度最终兜底: {amount} -> {formatted_amount}")

            # 🔥 强制市价单参数
            order_data = {
                "category": "spot",
                "symbol": bybit_symbol,
                "side": side_str.capitalize(),  # Bybit要求首字母大写
                "orderType": "Market",  # 🔥 强制市价单
                "qty": formatted_amount,  # 🔥 修复：使用精度处理后的数量
                "timeInForce": "IOC"  # 🔥 市价单使用IOC
            }
            
            # 🔥 关键修复：Bybit现货市价买单需要设置marketUnit为baseCoin
            # 根据Bybit官方文档：
            # marketUnit="baseCoin": qty表示基础币种数量（如8.76 DOT）
            # marketUnit="quoteCoin": qty表示报价币种金额（如8.76 USDT）
            # 默认是quoteCoin，需要明确设置为baseCoin来按币数量下单
            if side_str.lower() == "buy":
                order_data["marketUnit"] = "baseCoin"  # 🔥 解决下单金额计算错误的核心修复
                logger.info(f"🔧 Bybit现货市价买单设置marketUnit=baseCoin: {amount} {bybit_symbol.split('USDT')[0]}")

            logger.info(f"🔍 Bybit现货市价单: {order_data}")

            # 直接调用Bybit API
            response = await self._request(
                "POST", "/v5/order/create",
                data=order_data,
                signed=True
            )

            # 🔥 修复：Bybit API可能直接返回orderId，也可能包装在result中
            order_id = None
            if response:
                # 尝试从result字段获取
                if response.get("result") and response["result"].get("orderId"):
                    order_id = response["result"]["orderId"]
                # 尝试直接从响应获取
                elif response.get("orderId"):
                    order_id = response["orderId"]

                logger.info(f"✅ Bybit现货下单响应: order_id={order_id}, response={response}")

                if order_id:
                    # 🔥 关键修复：查询实际成交价格（与期货保持一致）
                    executed_price = await self._get_order_executed_price(order_id, symbol, "spot")

                    # 🔥 统一修复：确保Bybit返回标准字段，与Gate.io保持一致
                    result = {
                        "order_id": order_id,
                        "symbol": symbol,
                        "side": side_str,
                        "type": "market",  # 🔥 强制返回市价单
                        "amount": amount,  # 🔥 原始币数量（备用字段）
                        "actual_amount": float(formatted_amount),  # 🔥 实际下单数量
                        "filled": float(formatted_amount),  # 🔥 实际下单数量（标准字段）
                        "executed_quantity": float(formatted_amount),  # 🔥 关键修复：返回实际的格式化数量
                        "price": 0,  # 🔥 市价单价格为0
                        "executed_price": executed_price,  # 🔥 关键修复：返回实际成交价格
                        "average": executed_price,  # 🔥 平均成交价格（与Gate.io保持一致）
                        "status": "open",
                        "timestamp": int(time.time() * 1000)
                    }

                    logger.info(f"✅ Bybit现货返回: amount={result['amount']}, executed_price={executed_price:.8f}")
                    return result
                else:
                    raise Exception(f"Bybit现货下单失败: 未获取到订单ID - {response}")
            else:
                raise Exception(f"Bybit现货下单失败: 响应为空")

        except Exception as e:
            logger.error(f"Bybit现货直接下单异常: {e}")
            raise

    async def create_futures_order(self, symbol: str, side, order_type,
                                  amount: float, price: Optional[float] = None,
                                  params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        🔥 关键修复：直接期货下单方法，强制使用市价单
        专门为FuturesTrader提供的底层API调用
        """
        try:
            # 处理参数类型
            if hasattr(side, 'value'):
                side_str = side.value
            else:
                side_str = str(side).lower()

            # 🔥 强制使用市价单，禁止限价单
            order_type_str = "market"
            logger.info(f"🔥 Bybit期货强制使用市价单: {symbol} {side_str}")

            # 转换交易对格式
            bybit_symbol = self.currency_adapter.get_exchange_symbol(symbol, "bybit", "futures")

            # 🔥 使用统一精度处理修复小数位过多问题
            # 防御性检查：确保trading_rules_preloader属性存在
            if not hasattr(self, 'trading_rules_preloader') or self.trading_rules_preloader is None:
                logger.warning("⚠️ trading_rules_preloader不存在，重新初始化统一模块")
                from exchanges.unified_exchange_initializer import setup_exchange_unified
                setup_exchange_unified("Bybit", self)
            
            formatted_amount = await self.trading_rules_preloader.format_amount_with_contract_conversion(
                amount, self, symbol, "futures"
            )
            logger.info(f"🔧 Bybit期货精度处理: {amount} -> {formatted_amount}")

            # 🚨 关键修复：防御性精度验证，确保期货订单精度正确
            try:
                # 验证格式化后的数量是否符合Bybit期货API要求
                formatted_float = float(formatted_amount)
                formatted_str = str(formatted_float)

                # 检查小数位数是否过多
                if '.' in formatted_str:
                    decimal_places = len(formatted_str.split('.')[1])
                    if decimal_places > 5:  # Bybit期货通常支持最多5位小数
                        # 强制截取到5位小数
                        from decimal import Decimal, ROUND_DOWN
                        amount_decimal = Decimal(formatted_amount)
                        truncated = amount_decimal.quantize(Decimal('0.00001'), rounding=ROUND_DOWN)
                        formatted_amount = str(truncated)
                        logger.warning(f"⚠️ Bybit期货精度防御性截取: {formatted_float} -> {formatted_amount}")

                logger.debug(f"✅ Bybit期货精度验证通过: {formatted_amount}")

            except Exception as precision_error:
                logger.error(f"❌ Bybit期货精度验证失败: {precision_error}")
                # 兜底处理：使用5位小数截取
                from decimal import Decimal, ROUND_DOWN
                try:
                    amount_decimal = Decimal(str(amount))
                    truncated = amount_decimal.quantize(Decimal('0.00001'), rounding=ROUND_DOWN)
                    formatted_amount = str(truncated)
                    logger.warning(f"⚠️ Bybit期货精度兜底处理: {amount} -> {formatted_amount}")
                except:
                    formatted_amount = f"{amount:.5f}".rstrip('0').rstrip('.')
                    logger.error(f"❌ Bybit期货精度最终兜底: {amount} -> {formatted_amount}")

            # 🔥 强制市价单参数
            order_data = {
                "category": "linear",
                "symbol": bybit_symbol,
                "side": side_str.capitalize(),  # Bybit要求首字母大写
                "orderType": "Market",  # 🔥 强制市价单
                "qty": formatted_amount,  # 🔥 修复：使用精度处理后的数量
                "timeInForce": "IOC"  # 🔥 市价单使用IOC
            }

            # 处理额外参数
            if params:
                # 处理reduce_only参数
                if params.get("reduce_only") or params.get("is_closing"):
                    order_data["reduceOnly"] = True

                # 处理position_side参数（如果有）
                if "position_side" in params:
                    # Bybit的position side处理
                    pass  # Bybit通过side自动判断

            logger.info(f"🔍 Bybit期货市价单: {order_data}")

            # 直接调用Bybit API
            response = await self._request(
                "POST", "/v5/order/create",
                data=order_data,
                signed=True
            )

            # 🔥 修复：Bybit API可能直接返回orderId，也可能包装在result中
            order_id = None
            if response:
                # 尝试从result字段获取
                if response.get("result") and response["result"].get("orderId"):
                    order_id = response["result"]["orderId"]
                # 尝试直接从响应获取
                elif response.get("orderId"):
                    order_id = response["orderId"]

                logger.info(f"✅ Bybit期货下单响应: order_id={order_id}, response={response}")

                if order_id:
                    # 🔥 关键修复：查询实际成交价格
                    executed_price = await self._get_order_executed_price(order_id, symbol, "linear")

                    # 🔥 统一修复：确保Bybit返回标准字段，与Gate.io保持一致
                    result = {
                        "order_id": order_id,
                        "symbol": symbol,
                        "side": side_str,
                        "type": "market",  # 🔥 强制返回市价单
                        "amount": amount,  # 🔥 原始币数量（备用字段）
                        "actual_amount": float(formatted_amount),  # 🔥 实际下单数量
                        "filled": float(formatted_amount),  # 🔥 实际下单数量（标准字段）
                        "executed_quantity": float(formatted_amount),  # 🔥 关键修复：返回实际的格式化数量
                        "price": 0,  # 🔥 市价单价格为0
                        "executed_price": executed_price,  # 🔥 关键修复：返回实际成交价格
                        "average": executed_price,  # 🔥 平均成交价格（与Gate.io保持一致）
                        "status": "open",
                        "timestamp": int(time.time() * 1000)
                    }

                    logger.info(f"✅ Bybit期货返回: amount={result['amount']}, executed_price={executed_price:.8f}")
                    return result
                else:
                    raise Exception(f"Bybit期货下单失败: 未获取到订单ID - {response}")
            else:
                raise Exception(f"Bybit期货下单失败: 响应为空")

        except Exception as e:
            logger.error(f"Bybit期货直接下单异常: {e}")
            raise

    async def _get_order_executed_price(self, order_id: str, symbol: str, category: str) -> float:
        """
        🔥 关键修复：获取Bybit订单的实际成交价格
        解决Bybit期货API不返回executed_price的问题
        """
        try:
            # 转换为Bybit格式的symbol
            bybit_symbol = symbol.replace('-', '')

            # 查询订单详情
            params = {
                "category": category,
                "orderId": order_id
            }

            response = await self._request("GET", "/v5/order/realtime", params=params, signed=True)

            if response and "result" in response and "list" in response["result"]:
                order_list = response["result"]["list"]
                if order_list:
                    order_info = order_list[0]

                    # 尝试获取平均成交价格
                    avg_price = order_info.get("avgPrice")
                    if avg_price and float(avg_price) > 0:
                        executed_price = float(avg_price)
                        logger.info(f"🎯 Bybit订单{order_id}实际成交价格: {executed_price:.8f}")
                        return executed_price

            # 如果订单详情中没有，查询成交历史
            logger.warning(f"⚠️ 订单详情中未找到成交价格，查询成交历史: {order_id}")
            return await self._get_execution_history_price(order_id, bybit_symbol, category)

        except Exception as e:
            logger.error(f"❌ 获取Bybit订单{order_id}成交价格失败: {e}")
            # 返回0，让futures_trader.py使用兜底逻辑
            return 0.0

    async def _get_execution_history_price(self, order_id: str, bybit_symbol: str, category: str) -> float:
        """
        🔥 从成交历史获取实际成交价格（加权平均）
        """
        try:
            params = {
                "category": category,
                "symbol": bybit_symbol,
                "orderId": order_id,
                "limit": 50
            }

            response = await self._request("GET", "/v5/execution/list", params=params, signed=True)

            if response and "result" in response and "list" in response["result"]:
                executions = response["result"]["list"]

                if executions:
                    total_value = 0.0
                    total_qty = 0.0

                    for execution in executions:
                        price = float(execution.get("execPrice", 0))
                        qty = float(execution.get("execQty", 0))

                        if price > 0 and qty > 0:
                            total_value += price * qty
                            total_qty += qty

                    if total_qty > 0:
                        weighted_avg_price = total_value / total_qty
                        logger.info(f"🎯 Bybit订单{order_id}加权平均成交价格: {weighted_avg_price:.8f}")
                        return weighted_avg_price

            logger.warning(f"⚠️ 未找到订单{order_id}的成交历史")
            return 0.0

        except Exception as e:
            logger.error(f"❌ 查询Bybit订单{order_id}成交历史失败: {e}")
            return 0.0

    async def place_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        amount: float,
        price: Optional[float] = None,
        market_type: str = "spot",
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """下单 - 🔥 使用统一开仓管理器（已在__init__中初始化）"""
        try:
            # 🔥 删除重复逻辑，使用已初始化的统一开仓管理器
            # 处理参数类型
            if hasattr(side, 'value'):
                side_str = side.value
            else:
                side_str = str(side).lower()

            if hasattr(order_type, 'value'):
                order_type_str = order_type.value
            else:
                order_type_str = str(order_type).lower()

            # 🔥 修复：从params中提取orderbook参数
            orderbook = None
            if params and isinstance(params, dict):
                orderbook = params.get('orderbook')
                logger.info(f"🔍 Bybit从params中获取orderbook: {orderbook is not None}")

            # 准备开仓参数
            opening_params = await self.opening_manager.prepare_opening_params(
                symbol=symbol,
                side=side_str,
                quantity=amount,
                price=price,
                exchange=self,
                market_type=market_type,
                order_type=order_type_str
            )

            # 🔥 修复：执行开仓订单时传递orderbook参数
            result = await self.opening_manager.execute_opening_order(opening_params, self, orderbook)

            if result.success:
                return {
                    "order_id": result.order_id,
                    "symbol": symbol,
                    "side": side_str,
                    "type": order_type_str,
                    "amount": result.executed_quantity or amount,
                    "price": result.executed_price or price,
                    "status": "open",
                    "timestamp": int(time.time() * 1000)
                }
            else:
                raise Exception(f"Bybit下单失败: {result.error_message}")

        except Exception as e:
            self.error_count += 1
            logger.error(f"Bybit下单异常: {e}")
            raise

    async def cancel_order(self, order_id: str, symbol: str, market_type: str = "spot") -> bool:
        """取消订单"""
        try:
            symbol = self.currency_adapter.get_exchange_symbol(symbol, "bybit", market_type)
            
            response = await self._request(
                "POST", "/v5/order/cancel",
                data={
                    "category": "linear" if market_type == "futures" else "spot",
                    "symbol": symbol,
                    "orderId": order_id
                }
            )

            return True

        except Exception as e:
            # 🔥 使用统一错误处理
            if self._handle_common_errors(e, "cancel_order"):
                return True
            else:
                logger.error(f"Bybit取消订单失败: {e}")
                return False

    async def get_order(self, order_id: str, symbol: str, market_type: str = "spot") -> Dict[str, Any]:
        """查询订单状态"""
        try:
            symbol = self.currency_adapter.get_exchange_symbol(symbol, "bybit", market_type)
            
            params = {
                "category": "linear" if market_type == "futures" else "spot",
                "symbol": symbol,
                "orderId": order_id
            }

            response = await self._request(
                "GET", "/v5/order/realtime",
                params=params,
                signed=True
            )

            # 🔥 修复：安全处理空列表，防止list index out of range错误
            order_list = response.get("list", [])
            if not order_list:
                logger.warning(f"Bybit订单查询返回空列表: order_id={order_id}, symbol={symbol}")
                # 返回默认订单信息，表示订单不存在或已完成
                return {
                    "order_id": order_id,
                    "symbol": symbol,
                    "side": "",
                    "type": "unknown",
                    "amount": 0.0,
                    "price": 0.0,
                    "filled_amount": 0.0,
                    "status": "unknown",
                    "timestamp": int(time.time() * 1000)
                }

            order = order_list[0]

            return {
                "order_id": order.get("orderId", ""),
                "symbol": symbol,
                "side": order.get("side", "").lower(),
                "type": "limit" if order.get("orderType") == "Limit" else "market",
                "amount": float(order.get("qty", 0)),
                "price": float(order.get("price", 0)),
                "filled_amount": float(order.get("cumExecQty", 0)),
                "status": self._parse_order_status(order.get("orderStatus", "")).value,
                "timestamp": int(order.get("createdTime", 0))
            }

        except Exception as e:
            logger.error(f"Bybit查询订单失败: {e}")
            raise

    async def get_position(self, symbol: str = None) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        try:
            params = {"category": "linear", "settleCoin": "USDT"}
            
            if symbol:
                symbol = self.currency_adapter.get_exchange_symbol(symbol, "bybit", "futures")
                params["symbol"] = symbol

            response = await self._request(
                "GET", "/v5/position/list",
                params=params,
                signed=True
            )

            positions = []
            for pos in response.get("list", []):
                # 只收集有仓位的数据
                size = float(pos.get("size", 0))
                if size > 0:
                    positions.append({
                        "symbol": pos.get("symbol", ""),
                        "size": size,  # 仓位大小
                        "side": pos.get("side", "").lower(),  # 持仓方向，统一转为小写
                        "entry_price": float(pos.get("avgPrice", 0)),  # 开仓均价
                        "mark_price": float(pos.get("markPrice", 0)),  # 标记价格
                        "pnl": float(pos.get("unrealisedPnl", 0)),  # 未实现盈亏
                        "margin": float(pos.get("cumRealisedPnl", 0))  # 已实现盈亏
                    })

            logger.debug(f"Bybit期货持仓信息: {positions}")
            return positions

        except Exception as e:
            logger.error(f"获取Bybit持仓信息失败: {e}")
            return []

    async def close_position(self, symbol: str, amount: Optional[float] = None) -> Dict[str, Any]:
        """平仓（期货）- 🔥 使用统一平仓管理器（已在__init__中初始化）"""
        try:
            # 🔥 修复：close_position_unified不接受amount参数
            result = await self.closing_manager.close_position_unified(
                symbol=symbol,
                exchange=self,
                market_type="futures"
            )

            # 🔥 修复：统一返回格式，确保与其他交易所一致
            if hasattr(result, 'success'):
                return {
                    "success": result.success,
                    "order_id": getattr(result, 'order_id', ''),
                    "quantity": getattr(result, 'executed_quantity', 0),
                    "price": getattr(result, 'executed_price', 0),
                    "message": getattr(result, 'error_message', '') if not result.success else "平仓成功"
                }
            else:
                return result

        except Exception as e:
            logger.error(f"Bybit平仓失败: {e}")
            return {"success": False, "message": str(e), "closed_size": 0}

    async def transfer_funds(self, currency: str, amount: float,
                           from_account: AccountType, to_account: AccountType) -> bool:
            """内部转账（统一账户不需要）"""
            # Bybit统一账户不需要内部转账
            logger.info(f"Bybit统一账户不需要转账: {currency} {amount}")
            return True

    def is_unified_account(self) -> bool:
        """Bybit是统一账户"""
        return True

    def _convert_symbol(self, symbol: str, market_type: str = "spot") -> str:
        """转换交易对格式 - 使用统一适配器"""
        return self.currency_adapter.get_exchange_symbol(symbol, "bybit", market_type)

    def _parse_order_status(self, status: str) -> OrderStatus:
        """解析订单状态"""
        status_map = {
            "New": OrderStatus.OPEN,
            "PartiallyFilled": OrderStatus.PARTIAL,
            "Filled": OrderStatus.FILLED,
            "Cancelled": OrderStatus.CANCELLED,
            "Rejected": OrderStatus.FAILED
        }
        return status_map.get(status, OrderStatus.PENDING)

    async def set_leverage(self, symbol: str, leverage: int = None, margin_mode: str = "cross") -> Dict[str, Any]:
        """设置期货杠杆 - 🔥 修复：返回统一dict格式而非bool"""
        try:
            # 🔥 修复：统一使用3倍杠杆配置
            if leverage is None:
                leverage = int(os.getenv("BYBIT_LEVERAGE", "3"))
            max_leverage = float(os.getenv("MAX_LEVERAGE_RATIO", "3.0"))
            leverage = min(leverage, int(max_leverage))

            # 转换交易对格式为Bybit格式
            bybit_symbol = self.currency_adapter.get_exchange_symbol(symbol, "bybit", "futures")

            logger.info(f"Bybit设置杠杆: {symbol} -> {bybit_symbol} {leverage}倍（系统限制最大{max_leverage}倍）")

            # 构建请求参数
            data = {
                "category": "linear",
                "symbol": bybit_symbol,  # 使用正确的Bybit格式
                "buyLeverage": str(leverage),
                "sellLeverage": str(leverage)
            }

            # 使用统一的_request方法
            response = await self._request(
                "POST", "/v5/position/set-leverage",
                data=data,
                signed=True
            )

            logger.info(f"Bybit设置杠杆成功: {leverage}倍")
            return {"success": True, "leverage": leverage, "message": f"杠杆设置成功: {leverage}倍"}

        except Exception as e:
            # 🔥 使用统一错误处理
            if self._handle_common_errors(e, "set_leverage"):
                return {"success": True, "leverage": leverage, "message": "杠杆设置成功（通过错误处理）"}
            else:
                logger.error(f"❌ Bybit设置杠杆异常: {e}")
                # 🔥 关键：即使失败也不中断套利流程，返回统一格式
                return {"success": False, "leverage": leverage or 1, "message": f"杠杆设置失败: {str(e)}"}

    # 🔥 删除重复方法：使用统一精度处理系统
    # 价格格式化现在统一使用预加载器的format_amount_unified方法

    # 🔥 删除重复方法：使用基类的get_last_price方法

    # 🔥 删除重复方法：使用统一精度处理系统
    # 数量格式化现在统一使用预加载器的format_amount_unified方法

    # 🔥 删除重复的便利方法：直接使用基类的统一接口
    # get_orderbook(symbol, market_type, limit) 已经足够
    # get_balance(account_type) 已经足够
    # get_position(symbol) 已经足够

    async def get_server_time(self) -> int:
        """获取Bybit服务器时间 - 🔥 修复：添加超时机制防止阻塞"""
        try:
            # 🔥 修复：添加5秒超时，防止网络阻塞导致系统启动失败
            response = await asyncio.wait_for(
                self._request("GET", "/v5/market/time", signed=False),
                timeout=5.0
            )

            # 🔥 修复：Bybit V5 API返回格式分析和优化处理
            if isinstance(response, dict) and "result" in response:
                result = response["result"]

                # 🔥 优先使用timeNano（纳秒级，最精确）
                if "timeNano" in result:
                    try:
                        # timeNano是纳秒级字符串，转换为毫秒
                        time_nano = int(result["timeNano"])
                        server_time_ms = time_nano // 1000000
                        logger.debug(f"Bybit使用timeNano: {time_nano} -> {server_time_ms}ms")
                        return server_time_ms
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Bybit timeNano转换失败: {result['timeNano']}, 错误: {e}")

                # 🔥 备用方案：使用timeSecond（秒级）
                if "timeSecond" in result:
                    try:
                        # timeSecond是秒级字符串，转换为毫秒
                        time_second = int(result["timeSecond"])
                        server_time_ms = time_second * 1000
                        logger.debug(f"Bybit使用timeSecond: {time_second} -> {server_time_ms}ms")
                        return server_time_ms
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Bybit timeSecond转换失败: {result['timeSecond']}, 错误: {e}")

                # 🔥 第三备用方案：使用顶级time字段（毫秒级）
                if "time" in response:
                    try:
                        server_time_ms = int(response["time"])
                        logger.debug(f"Bybit使用顶级time字段: {server_time_ms}ms")
                        return server_time_ms
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Bybit顶级time字段转换失败: {response['time']}, 错误: {e}")

                # 🔥 详细日志：记录实际响应结构用于调试
                logger.warning(f"Bybit服务器时间响应格式异常，result内容: {result}")

            # 如果所有方法都失败，返回当前时间
            logger.warning("Bybit服务器时间响应格式异常，使用本地时间")
            return int(time.time() * 1000)

        except asyncio.TimeoutError:
            logger.warning("获取Bybit服务器时间超时(5秒)，使用本地时间")
            return int(time.time() * 1000)
        except Exception as e:
            logger.warning(f"获取Bybit服务器时间失败: {e}，使用本地时间")
            return int(time.time() * 1000)

    async def get_instruments_info(self, category: str, symbol: str = None) -> Dict[str, Any]:
        """
        🔥 关键修复：获取交易对信息 - 用于精度配置
        🎯 通用代币系统：优雅处理不存在的交易对，不报错
        """
        try:
            params = {"category": category}
            if symbol:
                params["symbol"] = symbol

            logger.debug(f"Bybit获取交易对信息: category={category}, symbol={symbol}")

            # 🔥 关键修复：_request返回的是result部分，需要包装成完整响应格式
            result_data = await self._request(
                "GET", "/v5/market/instruments-info",
                params=params,
                signed=False
            )

            # 🔥 修复：包装成期望的响应格式，与官方SDK保持一致
            response = {"result": result_data} if result_data else {"result": {"list": []}}

            logger.debug(f"Bybit交易对信息响应: {response}")
            return response

        except Exception as e:
            error_str = str(e)

            # 🔥 修复：交易对不存在是正常情况，不应该报错
            if "10001" in error_str and "symbol invalid" in error_str:
                if symbol:
                    logger.debug(f"🔍 Bybit交易对不存在: {symbol} (category={category}) - 优雅跳过")
                else:
                    logger.debug(f"🔍 Bybit类别无效: category={category} - 优雅跳过")
                # 返回空结果，表示该交易对不存在
                return {"result": {"list": []}}
            else:
                # 其他真正的错误才记录为ERROR
                logger.error(f"获取Bybit交易对信息失败: {e}")
                # 返回空结果，让调用方处理
                return {"result": {"list": []}}

    async def get_contract_info(self, symbol: str) -> Dict[str, Any]:
        """
        🔥 新增：获取单个合约信息 - 用于保证金计算
        支持任意币种的通用合约信息查询

        根据Bybit API文档：GET /v5/market/instruments-info?category=linear&symbol={symbol}
        返回instruments数据，包含杠杆等保证金计算相关字段
        """
        try:
            # 转换为Bybit期货合约格式
            contract_symbol = self.currency_adapter.get_exchange_symbol(symbol, "bybit", "futures")

            logger.debug(f"Bybit获取合约信息: {contract_symbol}")

            # 🔥 关键修复：_request返回的是result部分，直接使用
            result_data = await self._request(
                "GET", "/v5/market/instruments-info",
                params={"category": "linear", "symbol": contract_symbol},
                signed=False
            )

            if result_data and result_data.get("list"):
                contract_data = result_data["list"][0]

                # 根据Bybit API响应提取关键保证金计算字段
                leverage_filter = contract_data.get("leverageFilter", {})
                lot_size_filter = contract_data.get("lotSizeFilter", {})
                price_filter = contract_data.get("priceFilter", {})

                contract_info = {
                    "name": contract_data.get("symbol", contract_symbol),
                    "maintenance_rate": 0.005,  # Bybit永续合约一般维持保证金率0.5%
                    "leverage_min": float(leverage_filter.get("minLeverage", "1")),
                    "leverage_max": float(leverage_filter.get("maxLeverage", "100")),
                    "order_size_min": float(lot_size_filter.get("minOrderQty", "0.01")),
                    "order_size_max": float(lot_size_filter.get("maxOrderQty", "1000000")),
                    "tick_size": float(price_filter.get("tickSize", "0.01")),
                    "qty_step": float(lot_size_filter.get("qtyStep", "0.01")),
                    "contract_type": contract_data.get("contractType", "LinearPerpetual"),
                    "status": contract_data.get("status", "Trading"),
                    "base_coin": contract_data.get("baseCoin", ""),
                    "quote_coin": contract_data.get("quoteCoin", "USDT"),
                    "settle_coin": contract_data.get("settleCoin", "USDT"),
                    "launch_time": contract_data.get("launchTime", ""),
                    "delivery_time": contract_data.get("deliveryTime", ""),
                    "delivery_fee_rate": contract_data.get("deliveryFeeRate", ""),
                    "price_scale": contract_data.get("priceScale", ""),
                    "leverage_step": leverage_filter.get("leverageStep", "0.01"),
                    "min_price": price_filter.get("minPrice", ""),
                    "max_price": price_filter.get("maxPrice", "")
                }

                logger.info(f"✅ Bybit合约信息获取成功: {symbol} -> 最大杠杆={contract_info['leverage_max']}x")
                return contract_info
            else:
                logger.warning(f"⚠️ Bybit合约信息为空: {symbol}")
                return {}

        except Exception as e:
            logger.error(f"❌ Bybit获取合约信息失败 {symbol}: {e}")
            # 🔥 关键修复：不返回默认值，让调用方知道获取失败
            return {}

    # 🔥 已删除重复方法：_format_amount
    # 现在统一使用预加载器的格式化方法

    async def check_balance_before_order(self, symbol: str, side, amount: float,
                                       price: float = None, market_type: str = "spot") -> bool:
        """下单前检查余额是否充足"""
        try:
            # 🔥 关键修复：处理side参数类型
            if hasattr(side, 'value'):
                side_str = side.value
            else:
                side_str = str(side).lower()

            # 🔥 关键修复：Bybit是统一账户，应该始终使用UNIFIED账户类型
            balance = await self.get_balance(AccountType.UNIFIED)

            # 🔥 修复：获取实际价格进行准确计算
            if not price:
                try:
                    # 🔥 废弃：RestAPI价格获取已删除，使用OpportunityScanner.market_data
                    ticker = None  # await self.get_ticker(symbol, market_type)  # 已废弃
                    price = 1.0  # ticker.get("last", 1.0)  # 已废弃，使用默认价格
                    logger.debug(f"Bybit余额检查获取实际价格: {symbol} = ${price:.4f}")
                except Exception as e:
                    logger.warning(f"获取{symbol}价格失败，使用默认价格: {e}")
                    price = 1.0

            if market_type == "spot":
                if side_str == "buy":
                    # 现货买入：需要USDT余额
                    required_usdt = amount * price
                    usdt_info = balance.get("USDT", {})
                    
                    # 🔥 修复：兼容新的余额结构格式
                    if isinstance(usdt_info, dict):
                        available_usdt = float(usdt_info.get("available", 0))
                        wallet_balance = float(usdt_info.get("wallet_balance", 0))
                    else:
                        available_usdt = float(usdt_info) if usdt_info else 0
                        wallet_balance = available_usdt
                    
                    # 🔥 修复：更宽松的余额检查，考虑小数精度误差
                    balance_buffer = 0.95  # 5%的缓冲，应对手续费和精度误差
                    effective_available = available_usdt * balance_buffer
                    
                    if effective_available < required_usdt:
                        logger.error(f"Bybit现货买入余额不足: "
                                   f"需要{required_usdt:.2f} USDT, "
                                   f"可用{available_usdt:.2f} USDT, "
                                   f"有效可用{effective_available:.2f} USDT (考虑缓冲), "
                                   f"钱包余额{wallet_balance:.2f} USDT")
                        return False
                    
                    logger.info(f"Bybit现货买入余额检查通过: "
                              f"需要{required_usdt:.2f} USDT, "
                              f"可用{available_usdt:.2f} USDT, "
                              f"钱包余额{wallet_balance:.2f} USDT")
                else:
                    # 现货卖出：需要币种余额
                    base_currency = symbol.split('-')[0] if '-' in symbol else symbol.replace('USDT', '')
                    coin_info = balance.get(base_currency, {})
                    
                    # 🔥 修复：兼容新的余额结构格式
                    if isinstance(coin_info, dict):
                        available_coins = float(coin_info.get("available", 0))
                        wallet_balance = float(coin_info.get("wallet_balance", 0))
                    else:
                        available_coins = float(coin_info) if coin_info else 0
                        wallet_balance = available_coins
                    
                    # 🔥 修复关键问题：缓冲比例过于严格，调整为更合理的值
                    # 原来的99%太严格，导致7.7币可用但7.69币需要被拒绝
                    # 现在改为保留更小的安全边际，只应对真正的精度误差
                    balance_buffer = 0.995  # 0.5%的缓冲，仅应对精度误差而非过度保守
                    
                    # 🔥 修复关键问题：正确的余额检查逻辑
                    if available_coins >= amount:
                        # 余额充足，直接通过
                        logger.info(f"Bybit现货卖出余额检查通过: "
                                  f"需要{amount:.6f} {base_currency}, "
                                  f"可用{available_coins:.6f} {base_currency}, "
                                  f"钱包余额{wallet_balance:.6f} {base_currency}")
                        return True
                    else:
                        # 余额不足，计算差额
                        shortage_amount = amount - available_coins
                        shortage_percentage = shortage_amount / amount if amount > 0 else float('inf')

                        # 🔥 关键修复：基于日志分析，0.020084 >= 0.020000应该通过
                        # 如果差额很小（<1%），允许通过
                        if shortage_percentage < 0.01:  # 不足部分小于1%
                            logger.warning(f"Bybit现货卖出余额略微不足但在容忍范围内: "
                                         f"需要{amount:.6f} {base_currency}, "
                                         f"可用{available_coins:.6f} {base_currency}, "
                                         f"差额{shortage_amount:.6f} ({shortage_percentage*100:.3f}%) < 1%，允许通过")
                            return True
                        else:
                            # 余额确实不足
                            effective_available = available_coins * balance_buffer
                            logger.error(f"Bybit现货卖出余额不足: "
                                       f"需要{amount:.6f} {base_currency}, "
                                       f"可用{available_coins:.6f} {base_currency}, "
                                       f"有效可用{effective_available:.6f} {base_currency} (考虑缓冲), "
                                       f"钱包余额{wallet_balance:.6f} {base_currency}, "
                                       f"差额{shortage_amount:.6f} ({shortage_percentage*100:.3f}%)")
                            return False
                    
            else:  # futures
                # 期货：检查USDT保证金余额（UNIFIED账户）
                # 🔥 修复：使用统一保证金模块，不重复计算
                from utils.margin_calculator import get_margin_calculator
                margin_calculator = get_margin_calculator()
                required_margin, _ = await margin_calculator.calculate_required_margin(
                    "bybit", symbol, amount, price, self
                )
                usdt_info = balance.get("USDT", {})
                
                # 🔥 修复：兼容新的余额结构格式
                if isinstance(usdt_info, dict):
                    available_usdt = float(usdt_info.get("available", 0))
                    wallet_balance = float(usdt_info.get("wallet_balance", 0))
                else:
                    available_usdt = float(usdt_info) if usdt_info else 0
                    wallet_balance = available_usdt
                
                # 🔥 修复：期货保证金检查要更宽松，因为UNIFIED账户会自动管理保证金
                margin_buffer = 0.8  # 20%的缓冲，UNIFIED账户有自动保证金管理
                effective_available = available_usdt * margin_buffer
                
                if effective_available < required_margin:
                    logger.error(f"Bybit期货保证金不足: "
                               f"需要{required_margin:.2f} USDT, "
                               f"可用{available_usdt:.2f} USDT, "
                               f"有效可用{effective_available:.2f} USDT (考虑缓冲), "
                               f"钱包余额{wallet_balance:.2f} USDT, "
                               f"杠杆{self.default_leverage}x")
                    return False
                
                logger.info(f"Bybit期货保证金检查通过: "
                          f"需要{required_margin:.2f} USDT, "
                          f"可用{available_usdt:.2f} USDT, "
                          f"钱包余额{wallet_balance:.2f} USDT, "
                          f"杠杆{self.default_leverage}x")
                
            return True
            
        except Exception as e:
            logger.error(f"Bybit余额检查失败: {e}")
            # 🔥 保守策略：检查失败时返回False，避免余额不足的下单
            return False

    async def initialize(self) -> bool:
        """
        初始化Bybit交易所
        🔥 标准化接口：所有交易所都应该有initialize方法
        """
        try:
            logger.info("🚀 初始化Bybit交易所（统一账户模式）...")
            
            # 1. 测试连接
            server_time = await self.get_server_time()
            logger.info(f"✅ Bybit连接成功，服务器时间: {server_time}")
            
            # 2. 验证统一账户模式
            if self.is_unified_account():
                logger.info("✅ Bybit使用统一账户模式")
            else:
                logger.warning("⚠️ Bybit账户模式配置异常")
            
            # 3. 获取初始余额状态
            try:
                balance = await self.get_balance(AccountType.UNIFIED)
                total_usdt = balance.get("USDT", {})
                if isinstance(total_usdt, dict):
                    usdt_amount = total_usdt.get("available", 0)
                else:
                    usdt_amount = float(total_usdt) if total_usdt else 0
                
                logger.info(f"✅ Bybit初始余额: {usdt_amount:.2f} USDT")
            except Exception as e:
                logger.warning(f"⚠️ Bybit余额查询异常: {e}")
            
            # 4. 设置账户模式（如果需要）
            try:
                # Bybit通常默认就是统一账户，无需特殊设置
                logger.info("✅ Bybit统一账户模式已激活")
            except Exception as e:
                logger.warning(f"⚠️ Bybit账户模式设置警告: {e}")
            
            logger.info("✅ Bybit交易所初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ Bybit交易所初始化失败: {e}")
            return False

    async def get_instrument_info(self, symbol: str, market_type: str) -> Dict[str, Any]:
        """🔥 新增：获取交易对的配置信息 - 确保三交易所接口一致性"""
        try:
            # 转换交易对格式
            bybit_symbol = symbol.replace('-', '')

            # 检查缓存
            cache_key = f"{bybit_symbol}_{market_type}"
            if hasattr(self, 'instrument_info_cache') and cache_key in self.instrument_info_cache:
                logger.debug(f"Bybit使用缓存的交易对信息: {cache_key}")
                return self.instrument_info_cache[cache_key]

            # 初始化缓存
            if not hasattr(self, 'instrument_info_cache'):
                self.instrument_info_cache = {}

            # 从API获取
            logger.info(f"Bybit获取交易对信息: {bybit_symbol}, {market_type}")

            if market_type == "spot":
                # 现货交易对信息
                params = {"category": "spot", "symbol": bybit_symbol}
                response = await self._request("GET", "/v5/market/instruments-info", params=params)

                if response and "result" in response and "list" in response["result"]:
                    instruments = response["result"]["list"]
                    if instruments:
                        info = instruments[0]
                        result = {
                            "min_size": float(info.get("minOrderQty", 0.001)),
                            "size_increment": float(info.get("basePrecision", 0.001)),
                            "lot_size": float(info.get("minOrderQty", 0.001)),
                            "tick_size": float(info.get("tickSize", 0.1)),
                            "min_notional": float(info.get("minOrderAmt", 5.0))
                        }
                    else:
                        # 现货默认值
                        result = {
                            "min_size": 0.001,
                            "size_increment": 0.001,
                            "lot_size": 0.001,
                            "tick_size": 0.1,
                            "min_notional": 5.0
                        }
                else:
                    # 现货默认值
                    result = {
                        "min_size": 0.001,
                        "size_increment": 0.001,
                        "lot_size": 0.001,
                        "tick_size": 0.1,
                        "min_notional": 5.0
                    }
            else:
                # 期货交易对信息
                params = {"category": "linear", "symbol": bybit_symbol}
                response = await self._request("GET", "/v5/market/instruments-info", params=params)

                if response and "result" in response and "list" in response["result"]:
                    instruments = response["result"]["list"]
                    if instruments:
                        info = instruments[0]
                        result = {
                            "min_size": float(info.get("minOrderQty", 0.01)),
                            "size_increment": float(info.get("qtyStep", 0.01)),
                            "lot_size": float(info.get("qtyStep", 0.01)),
                            "tick_size": float(info.get("tickSize", 0.1)),
                            "min_notional": 5.0
                        }
                    else:
                        # 期货默认值
                        result = {
                            "min_size": 0.01,
                            "size_increment": 0.01,
                            "lot_size": 0.01,
                            "tick_size": 0.1,
                            "min_notional": 5.0
                        }
                else:
                    # 期货默认值
                    result = {
                        "min_size": 0.01,
                        "size_increment": 0.01,
                        "lot_size": 0.01,
                        "tick_size": 0.1,
                        "min_notional": 5.0
                    }

            # 缓存结果
            self.instrument_info_cache[cache_key] = result
            logger.debug(f"Bybit缓存交易对信息: {cache_key} -> {result}")

            return result

        except Exception as e:
            logger.error(f"Bybit获取交易对信息失败: {symbol}, {market_type}, 错误: {e}")
            # 返回默认值
            default_result = {
                "min_size": 0.001 if market_type == "spot" else 0.01,
                "size_increment": 0.001 if market_type == "spot" else 0.01,
                "lot_size": 0.001 if market_type == "spot" else 0.01,
                "tick_size": 0.1,
                "min_notional": 5.0
            }
            return default_result

    async def get_order_status(self, order_id: str, symbol: str, market_type: str = "spot") -> str:
        """
        🔥 新增：获取订单状态
        :param order_id: 订单ID
        :param symbol: 交易对
        :param market_type: 市场类型
        :return: 订单状态
        """
        try:
            order_info = await self.get_order(order_id, symbol, market_type)
            return order_info.get("status", "unknown")
        except Exception as e:
            self.logger.error(f"获取订单状态失败: {e}")
            return "error"

    async def get_trading_rules(self, symbol: str = None) -> Dict[str, Any]:
        """
        🔥 新增：获取交易规则
        :param symbol: 交易对，None表示所有
        :return: 交易规则
        """
        try:
            # 使用统一交易规则预加载器
            if hasattr(self, 'trading_rules_preloader') and self.trading_rules_preloader:
                if symbol:
                    # 获取特定交易对的规则
                    rules = await self.trading_rules_preloader.get_trading_rules(symbol, "bybit")
                    return {symbol: rules} if rules else {}
                else:
                    # 获取所有交易对的规则
                    return await self.trading_rules_preloader.get_all_trading_rules("bybit")
            else:
                self.logger.warning("交易规则预加载器不可用")
                return {}
        except Exception as e:
            self.logger.error(f"获取交易规则失败: {e}")
            return {}


# 测试代码
if __name__ == "__main__":
    import os
    from dotenv import load_dotenv

    load_dotenv()

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    async def test():
        # 从环境变量获取API密钥
        api_key = os.getenv("BYBIT_API_KEY", "")
        api_secret = os.getenv("BYBIT_API_SECRET", "")

        if not api_key or not api_secret:
            print("请设置BYBIT_API_KEY和BYBIT_API_SECRET环境变量")
            return

        exchange = BybitExchange(api_key, api_secret)

        try:
            # 测试连接
            print("测试连接...")
            connected = await exchange.test_connection()
            print(f"连接状态: {connected}")

            if connected:
                # 获取余额
                print("\n获取统一账户余额...")
                balance = await exchange.get_balance(AccountType.UNIFIED)
                print(f"余额: {balance}")

                # 获取行情
                print("\n获取BTC-USDT现货行情...")
                # 🔥 废弃：RestAPI价格获取已删除，使用OpportunityScanner.market_data
                ticker = {"last": 50000.0}  # await exchange.get_ticker("BTC-USDT", "spot")  # 已废弃
                print(f"行情: {ticker}")

                # 获取订单簿
                print("\n获取BTC-USDT现货深度...")
                orderbook = await exchange.get_orderbook("BTC-USDT", "spot")
                print(f"买单: {orderbook['bids'][:2]}")
                print(f"卖单: {orderbook['asks'][:2]}")

                if os.environ.get("TEST_PLACE_ORDER", "false").lower() == "true":
                    # 测试下单
                    print("\n测试下市价单...")
                    test_amount = float(os.environ.get("TEST_AMOUNT", "0.0001"))
                    # 🔥 市价单系统：删除价格参数，使用市价下单

                    try:
                        order = await exchange.place_order(
                            "BTC-USDT",
                            OrderSide.BUY,
                            OrderType.MARKET,
                            test_amount,
                            None,  # 市价单无需价格
                            "spot"
                        )
                        print(f"下单结果: {order}")

                        # 查询订单
                        order_info = await exchange.get_order(order["order_id"], "BTC-USDT", "spot")
                        print(f"订单状态: {order_info}")

                        # 取消订单
                        cancel_result = await exchange.cancel_order(order["order_id"], "BTC-USDT", "spot")
                        print(f"取消订单结果: {cancel_result}")
                    except Exception as e:
                        print(f"下单或取消订单失败: {e}")

        finally:
            await exchange.close()

    asyncio.run(test())