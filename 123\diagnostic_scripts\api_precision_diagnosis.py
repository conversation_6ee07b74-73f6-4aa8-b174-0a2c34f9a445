#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API精度失效诊断脚本
深度分析为什么API动态精度会失效，导致系统退化到兜底精度
"""

import os
import sys
import asyncio
import json
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def diagnose_api_precision_failure():
    """诊断API精度失效的根本原因"""
    print("🔍 开始诊断API精度失效原因")
    print("=" * 80)
    
    try:
        # 1. 检查交易所实例
        print("📋 步骤1: 检查交易所实例")

        # 尝试从全局交易所管理器获取
        exchanges = {}
        try:
            from core.global_exchange_manager import get_global_exchanges
            global_exchanges = get_global_exchanges()
            if global_exchanges:
                exchanges = global_exchanges
                print(f"   ✅ 使用全局交易所实例: {list(exchanges.keys())}")
            else:
                print(f"   ⚠️ 全局交易所实例为空，创建临时实例")
        except ImportError:
            print(f"   ⚠️ 全局交易所管理器不可用，创建临时实例")

        # 如果没有全局实例，创建临时实例
        if not exchanges:
            print("   🔧 创建临时交易所实例...")
            from exchanges.gate_exchange import GateExchange
            from exchanges.bybit_exchange import BybitExchange
            from exchanges.okx_exchange import OKXExchange

            # 从环境变量获取API密钥
            gate_api_key = os.getenv("GATE_API_KEY", "")
            gate_api_secret = os.getenv("GATE_API_SECRET", "")
            bybit_api_key = os.getenv("BYBIT_API_KEY", "")
            bybit_api_secret = os.getenv("BYBIT_API_SECRET", "")
            okx_api_key = os.getenv("OKX_API_KEY", "")
            okx_api_secret = os.getenv("OKX_API_SECRET", "")
            okx_passphrase = os.getenv("OKX_API_PASSPHRASE", "")

            # 创建实例
            if gate_api_key and gate_api_secret:
                exchanges['gate'] = GateExchange(gate_api_key, gate_api_secret)
            if bybit_api_key and bybit_api_secret:
                exchanges['bybit'] = BybitExchange(bybit_api_key, bybit_api_secret)
            if okx_api_key and okx_api_secret and okx_passphrase:
                exchanges['okx'] = OKXExchange(okx_api_key, okx_api_secret, okx_passphrase)

        for name, exchange in exchanges.items():
            if exchange:
                print(f"   ✅ {name}: {exchange.__class__.__name__}")
            else:
                print(f"   ❌ {name}: 未初始化")
        
        # 2. 测试RESOLV-USDT的API精度获取
        print(f"\n📋 步骤2: 测试RESOLV-USDT的API精度获取")
        symbol = "RESOLV-USDT"
        market_type = "spot"
        
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        for exchange_name, exchange_instance in exchanges.items():
            if not exchange_instance:
                continue
                
            print(f"\n🔧 测试 {exchange_name} API精度获取:")
            
            try:
                # 直接调用API精度获取方法
                precision_info = await preloader._get_precision_from_exchange_api(
                    exchange_instance, symbol, market_type
                )
                
                if precision_info:
                    print(f"   ✅ API精度获取成功:")
                    print(f"      step_size: {precision_info.get('step_size')}")
                    print(f"      amount_precision: {precision_info.get('amount_precision')}")
                    print(f"      min_amount: {precision_info.get('min_amount')}")
                    print(f"      source: {precision_info.get('source')}")
                else:
                    print(f"   ❌ API精度获取失败: 返回None")
                    
                    # 进一步诊断失败原因
                    if exchange_name == "bybit":
                        await diagnose_bybit_api_failure(exchange_instance, symbol, market_type)
                    elif exchange_name == "gate":
                        await diagnose_gate_api_failure(exchange_instance, symbol, market_type)
                    elif exchange_name == "okx":
                        await diagnose_okx_api_failure(exchange_instance, symbol, market_type)
                        
            except Exception as e:
                print(f"   ❌ API精度获取异常: {e}")
                import traceback
                print(f"      详细错误: {traceback.format_exc()}")
        
        # 3. 检查缓存机制
        print(f"\n📋 步骤3: 检查缓存机制")
        cache_key = f"bybit_{symbol}_{market_type}"
        
        if hasattr(preloader, 'precision_cache') and cache_key in preloader.precision_cache:
            cached_data = preloader.precision_cache[cache_key]
            print(f"   ✅ 找到精度缓存: {cache_key}")
            print(f"      数据: {cached_data.get('data')}")
            print(f"      来源: {cached_data.get('source')}")
            print(f"      缓存时间: {cached_data.get('cache_time')}")
        else:
            print(f"   ❌ 未找到精度缓存: {cache_key}")
            
            # 显示所有缓存键
            if hasattr(preloader, 'precision_cache'):
                print(f"   📋 现有缓存键:")
                for key in preloader.precision_cache.keys():
                    if "RESOLV" in key or "bybit" in key:
                        print(f"      {key}")
        
        # 4. 检查交易规则缓存
        print(f"\n📋 步骤4: 检查交易规则缓存")
        rule_key = f"bybit_{symbol}_{market_type}"
        
        if hasattr(preloader, 'trading_rules') and rule_key in preloader.trading_rules:
            rule = preloader.trading_rules[rule_key]
            print(f"   ✅ 找到交易规则: {rule_key}")
            print(f"      qty_step: {rule.qty_step}")
            print(f"      qty_precision: {rule.qty_precision}")
            print(f"      timestamp: {rule.timestamp}")
        else:
            print(f"   ❌ 未找到交易规则: {rule_key}")
            
            # 显示所有规则键
            if hasattr(preloader, 'trading_rules'):
                print(f"   📋 现有规则键:")
                for key in preloader.trading_rules.keys():
                    if "RESOLV" in key or "bybit" in key:
                        print(f"      {key}")
        
        # 5. 测试格式化流程
        print(f"\n📋 步骤5: 测试格式化流程")
        test_amount = 173.01038062283735
        
        try:
            formatted = preloader.format_amount_unified(test_amount, "bybit", symbol, market_type)
            print(f"   格式化结果: {test_amount} -> '{formatted}'")
            
            # 检查是否使用了兜底精度
            if "0.001" in str(formatted) or len(str(formatted).split('.')[-1]) <= 3:
                print(f"   ⚠️ 疑似使用了兜底精度（3位小数）")
            else:
                print(f"   ✅ 使用了高精度格式化")
                
        except Exception as e:
            print(f"   ❌ 格式化失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def diagnose_bybit_api_failure(exchange, symbol, market_type):
    """诊断Bybit API失效的具体原因"""
    print(f"      🔍 深度诊断Bybit API失效:")
    
    try:
        # 检查get_instruments_info方法是否存在
        if not hasattr(exchange, 'get_instruments_info'):
            print(f"         ❌ 缺少get_instruments_info方法")
            return
        
        # 测试API调用
        category = "spot" if market_type == "spot" else "linear"
        
        # 获取交易所格式的symbol
        from core.universal_token_system import get_universal_token_system
        token_system = get_universal_token_system()
        exchange_symbol = token_system.get_exchange_symbol_format(symbol, "bybit", market_type)
        
        print(f"         交易所格式: {symbol} -> {exchange_symbol}")
        print(f"         API类别: {category}")
        
        # 直接调用API
        response = await exchange.get_instruments_info(category, exchange_symbol)
        print(f"         API响应: {json.dumps(response, indent=2)}")
        
        # 检查响应结构
        if response and "result" in response:
            result = response["result"]
            if "list" in result and result["list"]:
                instruments = result["list"]
                print(f"         ✅ 找到{len(instruments)}个交易对")
                
                instrument = instruments[0]
                lot_size_filter = instrument.get("lotSizeFilter", {})
                price_filter = instrument.get("priceFilter", {})
                
                print(f"         lotSizeFilter: {lot_size_filter}")
                print(f"         priceFilter: {price_filter}")
                
                # 检查关键字段
                if market_type == "spot":
                    base_precision = lot_size_filter.get("basePrecision")
                    print(f"         basePrecision: {base_precision}")
                else:
                    qty_step = lot_size_filter.get("qtyStep")
                    print(f"         qtyStep: {qty_step}")
            else:
                print(f"         ❌ API返回空的交易对列表")
        else:
            print(f"         ❌ API返回格式异常")
            
    except Exception as e:
        print(f"         ❌ Bybit API调用失败: {e}")

async def diagnose_gate_api_failure(exchange, symbol, market_type):
    """诊断Gate API失效的具体原因"""
    print(f"      🔍 深度诊断Gate API失效:")
    
    try:
        # 检查get_currency_pairs方法是否存在
        if not hasattr(exchange, 'get_currency_pairs'):
            print(f"         ❌ 缺少get_currency_pairs方法")
            return
        
        # 获取交易所格式的symbol
        from core.universal_token_system import get_universal_token_system
        token_system = get_universal_token_system()
        exchange_symbol = token_system.get_exchange_symbol_format(symbol, "gate", market_type)
        
        print(f"         交易所格式: {symbol} -> {exchange_symbol}")
        
        # 直接调用API
        pairs_info = await exchange.get_currency_pairs()
        print(f"         API返回{len(pairs_info) if pairs_info else 0}个交易对")
        
        # 查找目标交易对
        found_pair = None
        for pair in pairs_info or []:
            if pair.get("id") == exchange_symbol:
                found_pair = pair
                break
        
        if found_pair:
            print(f"         ✅ 找到交易对: {found_pair}")
            amount_precision = found_pair.get("amount_precision")
            print(f"         amount_precision: {amount_precision}")
        else:
            print(f"         ❌ 未找到交易对: {exchange_symbol}")
            
    except Exception as e:
        print(f"         ❌ Gate API调用失败: {e}")

async def diagnose_okx_api_failure(exchange, symbol, market_type):
    """诊断OKX API失效的具体原因"""
    print(f"      🔍 深度诊断OKX API失效:")
    
    try:
        # 检查相关方法是否存在
        print(f"         检查API方法可用性...")
        
        # 获取交易所格式的symbol
        from core.universal_token_system import get_universal_token_system
        token_system = get_universal_token_system()
        exchange_symbol = token_system.get_exchange_symbol_format(symbol, "okx", market_type)
        
        print(f"         交易所格式: {symbol} -> {exchange_symbol}")
        
        # OKX通常使用不同的API方法
        print(f"         ⚠️ OKX API精度获取方法待实现")
            
    except Exception as e:
        print(f"         ❌ OKX API调用失败: {e}")

async def main():
    """主诊断函数"""
    print("🚀 API精度失效深度诊断")
    print("=" * 100)
    
    success = await diagnose_api_precision_failure()
    
    print(f"\n📊 诊断总结:")
    print("=" * 100)
    
    if success:
        print("✅ 诊断完成，请查看上述详细信息")
        print("\n🎯 可能的失效原因:")
        print("1. 交易所API返回空数据或格式异常")
        print("2. 交易对在某些交易所不存在")
        print("3. API调用权限或网络问题")
        print("4. 缓存机制未正确工作")
        print("5. 交易所格式转换错误")
    else:
        print("❌ 诊断失败，请检查系统配置")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
