2025-07-31 10:04:53 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-31 10:04:53 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-31 10:04:53 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 10:04:53 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 10:04:53.573 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-31 10:04:53.573 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-31 10:04:53 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-31 10:04:53 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-31 10:04:53 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-31 10:04:54 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-31 10:04:54.193 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:04:54.689 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:04:54.689 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:04:55.185 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:04:55.185 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:04:55 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-31 10:04:55.185 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:04:55.684 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:04:55.684 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:04:56.191 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:04:56.191 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:04:56 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-31 10:04:56.192 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:04:56.680 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:04:56.680 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:04:57.182 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:04:57.183 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:04:57 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-31 10:04:57.183 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:04:57.696 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:04:57.696 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:04:58.192 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:04:58.192 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:04:58 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-31 10:04:58 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-31 10:04:58.688 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 10:04:59.888 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:05:01.394 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:05:02.403 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:05:03.010 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:05:04.369 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:05:06.954 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:05:09.530 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:05:23.963 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:05:23.963 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:05:24.968 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:05:24.969 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:05:25.965 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:05:25.965 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:05:26.959 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:05:26.959 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:05:27.963 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:05:27.963 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:05:28.969 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:05:28.969 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:13.962 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 10:06:14.466 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:06:14.498 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-07-31 10:06:14.500 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:06:14.501 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:06:17.084 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-07-31 10:06:17.572 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-07-31 10:06:17.574 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-07-31 10:06:17.578 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-07-31 10:06:17.601 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-07-31 10:06:18.603 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.104 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.104 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.104 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.104 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.104 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.105 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.105 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.105 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.105 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 10:06:19.179 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:06:19.179 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:19.687 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:06:19.687 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:19.690 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:06:19.690 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:19.698 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.698 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.698 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.699 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.699 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.699 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.700 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.700 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.700 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.700 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:19.701 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:19.701 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:19.706 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:06:19.706 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:19.707 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.709 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.709 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.709 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.709 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.713 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.713 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.713 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:19.717 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.717 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.719 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:19.720 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:06:19.720 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:19.798 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.800 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.800 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.800 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.800 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:06:20.181 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:20.181 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:20.185 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:20.186 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:20.187 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:20.187 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:20.189 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:20.190 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:20.191 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:20.191 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:20.192 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:20.192 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:20.193 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:20.194 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:20.197 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:20.197 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:20.199 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 10:06:20.199 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 10:06:30.978 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 10:06:32.275 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:06:33.776 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:06:34.777 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:06:35.357 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:06:35.845 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:06:36.418 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:06:36.999 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:06:41.685 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 10:06:43.211 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:06:44.713 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:06:45.708 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:06:46.285 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 10:06:46.805 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:06:47.373 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:06:47.983 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 10:16:15.481 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:16:16.864 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-07-31 10:16:16.864 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 10:16:17.402 [INFO] [FuturesTrader.okx] 🔍 [DEBUG] 期货market_sell调用: symbol=RESOLV-USDT, amount=172.583820, slippage=None, disable_split=True
2025-07-31 10:16:17.402 [INFO] [FuturesTrader.okx] 期货市价卖单使用传入的订单簿数据: RESOLV-USDT, asks=30, bids=30
2025-07-31 10:16:17.402 [INFO] [FuturesTrader.okx] 🎯 期货卖出: RESOLV-USDT 172.58382 (精度已由统一系统处理)
2025-07-31 10:16:17.402 [INFO] [FuturesTrader.okx] ✅ 期货卖出使用30档纯净执行价格: 0.*************-07-31 10:16:17.403 [INFO] [FuturesTrader.okx] 🔧 传递position_side参数给期货交易所: short
2025-07-31 10:16:17.403 [INFO] [exchanges.okx_exchange] 🔥 OKX期货强制使用市价单: RESOLV-USDT sell
2025-07-31 10:16:18.030 [INFO] [exchanges.okx_exchange] 🔧 OKX账户配置: 账户等级=2, 持仓模式=net_mode
2025-07-31 10:16:18.463 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:16:18.463 [INFO] [exchanges.okx_exchange] 🔧 OKX期货开仓模式: 合约转换 172.58382 -> 17.0
2025-07-31 10:16:18.463 [INFO] [exchanges.okx_exchange] 🔧 OKX期货统一精度处理: 172.58382 -> 17.0
2025-07-31 10:16:18.463 [INFO] [exchanges.okx_exchange] 🔧 OKX单币种保证金模式，设置tdMode=isolated
2025-07-31 10:16:18.463 [INFO] [exchanges.okx_exchange] 🔧 OKX买卖模式，设置posSide: net
2025-07-31 10:16:18.463 [INFO] [exchanges.okx_exchange] 🔍 OKX期货市价单（修复后）: {'instId': 'RESOLV-USDT-SWAP', 'side': 'sell', 'ordType': 'market', 'sz': '17.0', 'tdMode': 'isolated', 'posSide': 'net'}
2025-07-31 10:16:18.463 [INFO] [exchanges.okx_exchange] 🚀 OKX期货下单请求详细信息:
2025-07-31 10:16:18.463 [INFO] [exchanges.okx_exchange]    原始数量: 172.58382
2025-07-31 10:16:18.464 [INFO] [exchanges.okx_exchange]    格式化数量: 17.0
2025-07-31 10:16:18.464 [INFO] [exchanges.okx_exchange]    API sz参数: '17.0'
2025-07-31 10:16:18.464 [INFO] [exchanges.okx_exchange]    完整请求: {'instId': 'RESOLV-USDT-SWAP', 'side': 'sell', 'ordType': 'market', 'sz': '17.0', 'tdMode': 'isolated', 'posSide': 'net'}
2025-07-31 10:16:18.973 [INFO] [exchanges.okx_exchange] 🔍 OKX期货下单响应: [{'clOrdId': '', 'ordId': '2732920537643540480', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '1753949778922'}]
2025-07-31 10:16:18.973 [INFO] [exchanges.okx_exchange] 🎯 OKX期货API执行验证:
2025-07-31 10:16:18.973 [INFO] [exchanges.okx_exchange]    订单ID: 2732920537643540480
2025-07-31 10:16:18.973 [INFO] [exchanges.okx_exchange]    请求sz: '17.0'
2025-07-31 10:16:18.973 [INFO] [exchanges.okx_exchange]    响应数据: {'clOrdId': '', 'ordId': '2732920537643540480', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '1753949778922'}
2025-07-31 10:16:18.973 [ERROR] [exchanges.okx_exchange] ❌ 检测到数量不匹配!
2025-07-31 10:16:18.973 [ERROR] [exchanges.okx_exchange]    原始数量: 172.58382
2025-07-31 10:16:18.973 [ERROR] [exchanges.okx_exchange]    API数量: 17.0
2025-07-31 10:16:18.974 [ERROR] [exchanges.okx_exchange]    放大倍数: 0.10x
2025-07-31 10:16:19.559 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 错误代码: 51603
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 错误信息: Order does not exist
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'code': '51603', 'data': [], 'msg': 'Order does not exist'}
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/trade/order
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Order does not exist (代码: 51603)',)
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange] ❌ 获取OKX订单2732920537643540480成交价格失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-31 10:16:19.561 [INFO] [exchanges.okx_exchange] ✅ OKX期货返回: amount=172.58382, executed_price=0.00000000
2025-07-31 10:16:19.561 [WARNING] [FuturesTrader.okx] ⚠️ 无法获取okx期货实际成交价格，使用30档算法价格: 0.*************-07-31 10:16:19.561 [WARNING] [FuturesTrader.okx] ⚠️ API响应: {'order_id': '2732920537643540480', 'symbol': 'RESOLV-USDT', 'side': 'sell', 'type': 'market', 'amount': 172.58382, 'actual_amount': 17.0, 'filled': 17.0, 'executed_quantity': 17.0, 'price': 0, 'executed_price': 0.0, 'average': 0.0, 'status': 'open', 'timestamp': 1753949779561}
2025-07-31 10:16:19.561 [INFO] [FuturesTrader.okx] ✅ 使用兜底机制继续执行，确保交易不中断
2025-07-31 10:16:21.331 [INFO] [exchanges.okx_exchange] 🔥 OKX期货强制使用市价单: RESOLV-USDT buy
2025-07-31 10:16:21.854 [INFO] [exchanges.okx_exchange] 🔧 OKX账户配置: 账户等级=2, 持仓模式=net_mode
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange] 🔧 OKX期货平仓模式: 跳过合约转换，直接使用 17.0
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange] 🔧 OKX期货统一精度处理: 17.0 -> 17.0
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange] 🔧 OKX单币种保证金模式，设置tdMode=isolated
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange] 🔧 OKX买卖模式，设置posSide: net
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange] 🔍 OKX期货市价单（修复后）: {'instId': 'RESOLV-USDT-SWAP', 'side': 'buy', 'ordType': 'market', 'sz': '17.0', 'tdMode': 'isolated', 'posSide': 'net'}
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange] 🚀 OKX期货下单请求详细信息:
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange]    原始数量: 17.0
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange]    格式化数量: 17.0
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange]    API sz参数: '17.0'
2025-07-31 10:16:21.855 [INFO] [exchanges.okx_exchange]    完整请求: {'instId': 'RESOLV-USDT-SWAP', 'side': 'buy', 'ordType': 'market', 'sz': '17.0', 'tdMode': 'isolated', 'posSide': 'net'}
2025-07-31 10:16:22.403 [INFO] [exchanges.okx_exchange] 🔍 OKX期货下单响应: [{'clOrdId': '', 'ordId': '2732920652097708032', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '1753949782333'}]
2025-07-31 10:16:22.403 [INFO] [exchanges.okx_exchange] 🎯 OKX期货API执行验证:
2025-07-31 10:16:22.403 [INFO] [exchanges.okx_exchange]    订单ID: 2732920652097708032
2025-07-31 10:16:22.403 [INFO] [exchanges.okx_exchange]    请求sz: '17.0'
2025-07-31 10:16:22.403 [INFO] [exchanges.okx_exchange]    响应数据: {'clOrdId': '', 'ordId': '2732920652097708032', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '1753949782333'}
2025-07-31 10:16:22.403 [INFO] [exchanges.okx_exchange] ✅ 数量匹配正确: 17.0 = 17.0
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange]   - 错误代码: 51603
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange]   - 错误信息: Order does not exist
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'code': '51603', 'data': [], 'msg': 'Order does not exist'}
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/trade/order
2025-07-31 10:16:22.940 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:16:22.940 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-31 10:16:22.940 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Order does not exist (代码: 51603)',)
2025-07-31 10:16:22.940 [ERROR] [exchanges.okx_exchange] ❌ 获取OKX订单2732920652097708032成交价格失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-31 10:16:22.940 [INFO] [exchanges.okx_exchange] ✅ OKX期货返回: amount=17.0, executed_price=0.00000000
2025-07-31 10:16:24.540 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:16:26.247 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:16:26.248 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:16:26.248 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 10:16:26.249 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 17:04:11 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-31 17:04:11 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-31 17:04:11 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 17:04:11 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 17:04:11 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
