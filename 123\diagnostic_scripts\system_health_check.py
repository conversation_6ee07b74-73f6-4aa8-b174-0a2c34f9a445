#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统健康检查脚本
确保精度修复不影响其他交易对和交易所的正常运行
"""

import os
import sys
import asyncio
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def test_multiple_exchanges():
    """测试多个交易所的精度处理"""
    print("🔍 测试多个交易所精度处理一致性")
    print("=" * 80)
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 测试不同交易所和交易对
        test_cases = [
            {"exchange": "bybit", "symbol": "BTC-USDT", "market_type": "spot", "amount": 0.001234567},
            {"exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "amount": 1.23456789},
            {"exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "amount": 173.01038062283735},
            {"exchange": "gate", "symbol": "BTC_USDT", "market_type": "spot", "amount": 0.001234567},
            {"exchange": "gate", "symbol": "ETH_USDT", "market_type": "spot", "amount": 1.23456789},
            {"exchange": "okx", "symbol": "BTC-USDT", "market_type": "spot", "amount": 0.001234567},
            {"exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "amount": 1.23456789},
        ]
        
        print(f"📊 测试案例数量: {len(test_cases)}")
        
        all_passed = True
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n🔧 案例 {i}: {case['exchange']} {case['symbol']} {case['market_type']}")
            print(f"   原始数量: {case['amount']}")
            
            try:
                # 获取交易规则
                rule = preloader.get_trading_rule(case['exchange'], case['symbol'], case['market_type'])
                if rule:
                    print(f"   交易规则: qty_step={rule.qty_step}, precision={rule.qty_precision}")
                    
                    # 格式化数量
                    formatted = preloader.format_amount_unified(
                        case['amount'], case['exchange'], case['symbol'], case['market_type']
                    )
                    print(f"   格式化结果: '{formatted}'")
                    
                    # 验证精度
                    formatted_float = float(formatted)
                    formatted_str = str(formatted_float)
                    
                    if '.' in formatted_str:
                        decimal_places = len(formatted_str.split('.')[1])
                        max_expected = 8  # 最大预期精度
                        if decimal_places <= max_expected:
                            print(f"   ✅ 精度检查通过 ({decimal_places}位小数)")
                        else:
                            print(f"   ❌ 精度检查失败 ({decimal_places}位小数 > {max_expected})")
                            all_passed = False
                    else:
                        print(f"   ✅ 整数格式")
                    
                    # 验证步长一致性
                    amount_decimal = Decimal(formatted)
                    step_decimal = Decimal(str(rule.qty_step))
                    remainder = amount_decimal % step_decimal
                    
                    if remainder == 0:
                        print(f"   ✅ 步长验证通过")
                    else:
                        print(f"   ❌ 步长验证失败 (余数={remainder})")
                        all_passed = False
                        
                else:
                    print(f"   ⚠️ 交易规则获取失败，使用兜底处理")
                    # 测试兜底处理
                    formatted = preloader.format_amount_unified(
                        case['amount'], case['exchange'], case['symbol'], case['market_type']
                    )
                    print(f"   兜底格式化结果: '{formatted}'")
                    
                    # 验证兜底精度（应该是0.000001）
                    formatted_float = float(formatted)
                    formatted_str = str(formatted_float)
                    
                    if '.' in formatted_str:
                        decimal_places = len(formatted_str.split('.')[1])
                        if decimal_places <= 6:  # 修复后的兜底精度
                            print(f"   ✅ 兜底精度检查通过 ({decimal_places}位小数)")
                        else:
                            print(f"   ❌ 兜底精度检查失败 ({decimal_places}位小数)")
                            all_passed = False
                    else:
                        print(f"   ✅ 兜底整数格式")
                
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 多交易所测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_edge_cases():
    """测试边界情况"""
    print(f"\n🔍 测试边界情况:")
    print("=" * 80)
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 边界测试案例
        edge_cases = [
            {"desc": "极小数量", "amount": 0.000001, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot"},
            {"desc": "极大数量", "amount": 999999.999999, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot"},
            {"desc": "整数", "amount": 100, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot"},
            {"desc": "高精度小数", "amount": 123.123456789012345, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot"},
            {"desc": "科学计数法", "amount": 1.23e-6, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot"},
        ]
        
        print(f"📊 边界测试案例数量: {len(edge_cases)}")
        
        all_passed = True
        
        for i, case in enumerate(edge_cases, 1):
            print(f"\n🔧 边界案例 {i}: {case['desc']}")
            print(f"   原始数量: {case['amount']}")
            
            try:
                formatted = preloader.format_amount_unified(
                    case['amount'], case['exchange'], case['symbol'], case['market_type']
                )
                print(f"   格式化结果: '{formatted}'")
                
                # 验证结果有效性
                formatted_float = float(formatted)
                if formatted_float > 0:
                    print(f"   ✅ 结果有效 ({formatted_float})")
                else:
                    print(f"   ❌ 结果无效 ({formatted_float})")
                    all_passed = False
                
                # 验证精度合理性
                formatted_str = str(formatted_float)
                if '.' in formatted_str:
                    decimal_places = len(formatted_str.split('.')[1])
                    if decimal_places <= 8:  # 合理的精度范围
                        print(f"   ✅ 精度合理 ({decimal_places}位小数)")
                    else:
                        print(f"   ❌ 精度过高 ({decimal_places}位小数)")
                        all_passed = False
                else:
                    print(f"   ✅ 整数结果")
                
            except Exception as e:
                print(f"   ❌ 边界测试失败: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 边界测试失败: {e}")
        return False

async def test_performance_impact():
    """测试性能影响"""
    print(f"\n🔍 测试性能影响:")
    print("=" * 80)
    
    try:
        import time
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 性能测试
        test_amount = 173.01038062283735
        exchange = "bybit"
        symbol = "RESOLV-USDT"
        market_type = "spot"
        
        # 预热
        for _ in range(5):
            preloader.format_amount_unified(test_amount, exchange, symbol, market_type)
        
        # 性能测试
        iterations = 1000
        start_time = time.time()
        
        for _ in range(iterations):
            formatted = preloader.format_amount_unified(test_amount, exchange, symbol, market_type)
        
        end_time = time.time()
        total_time = (end_time - start_time) * 1000  # 转换为毫秒
        avg_time = total_time / iterations
        
        print(f"📊 性能测试结果:")
        print(f"   测试次数: {iterations}")
        print(f"   总耗时: {total_time:.2f}ms")
        print(f"   平均耗时: {avg_time:.4f}ms")
        
        # 性能标准：平均每次调用应该小于1ms
        if avg_time < 1.0:
            print(f"   ✅ 性能测试通过 (< 1ms)")
            return True
        else:
            print(f"   ❌ 性能测试失败 (>= 1ms)")
            return False
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

async def main():
    """主健康检查函数"""
    print("🚀 开始系统健康检查")
    print("=" * 100)
    
    # 1. 测试多个交易所
    multi_exchange_passed = await test_multiple_exchanges()
    
    # 2. 测试边界情况
    edge_cases_passed = await test_edge_cases()
    
    # 3. 测试性能影响
    performance_passed = await test_performance_impact()
    
    print(f"\n📊 系统健康检查总结:")
    print("=" * 100)
    
    if multi_exchange_passed:
        print("✅ 多交易所一致性测试通过")
    else:
        print("❌ 多交易所一致性测试失败")
    
    if edge_cases_passed:
        print("✅ 边界情况测试通过")
    else:
        print("❌ 边界情况测试失败")
    
    if performance_passed:
        print("✅ 性能影响测试通过")
    else:
        print("❌ 性能影响测试失败")
    
    overall_health = multi_exchange_passed and edge_cases_passed and performance_passed
    
    if overall_health:
        print("\n🎉 系统健康检查完全通过！")
        print("🎯 修复质量确认:")
        print("   ✅ 不影响其他交易所和交易对")
        print("   ✅ 边界情况处理正确")
        print("   ✅ 性能影响在可接受范围内")
        print("   ✅ 系统整体稳定性良好")
        print("\n🚀 精度修复已完美集成到系统中！")
    else:
        print("\n❌ 系统健康检查发现问题，需要进一步调试")
    
    return overall_health

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
