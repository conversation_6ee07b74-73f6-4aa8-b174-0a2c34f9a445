#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的精度解决方案
基于真相：API没有错误，是我们的交易逻辑需要适应交易所规则
"""

import logging
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Any, Optional
import time

logger = logging.getLogger(__name__)

class CorrectPrecisionSolution:
    """
    正确的精度解决方案
    
    核心原则：
    1. API返回的精度是正确的，必须严格遵守
    2. 调整我们的交易数量以符合交易所规则
    3. 不要试图"修复"API，而是适应API
    4. 保持三交易所一致的处理逻辑
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 缓存精度调整结果，提高性能
        self.adjustment_cache = {}
        self.cache_ttl = 300  # 5分钟缓存
    
    def adjust_quantity_to_exchange_rules(self, quantity: float, step_size: float, 
                                        exchange: str, symbol: str) -> Dict[str, Any]:
        """
        调整交易数量以符合交易所规则
        这是核心方法：不是修改精度，而是修改数量
        """
        
        # 检查缓存
        cache_key = f"{exchange}_{symbol}_{quantity}_{step_size}"
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result
        
        try:
            # 使用Decimal确保精度计算准确
            quantity_decimal = Decimal(str(quantity))
            step_decimal = Decimal(str(step_size))
            
            # 计算符合步长的最大数量（向下取整）
            steps = quantity_decimal // step_decimal
            adjusted_quantity = steps * step_decimal
            
            # 转换回float
            adjusted_float = float(adjusted_quantity)
            
            # 计算调整信息
            adjustment_info = {
                "original_quantity": quantity,
                "adjusted_quantity": adjusted_float,
                "step_size": step_size,
                "adjustment_amount": quantity - adjusted_float,
                "adjustment_percentage": ((quantity - adjusted_float) / quantity * 100) if quantity > 0 else 0,
                "exchange": exchange,
                "symbol": symbol,
                "compliant": True,
                "method": "quantity_adjustment"
            }
            
            # 记录调整过程
            if adjustment_info["adjustment_amount"] > 0:
                self.logger.info(f"📏 数量调整: {symbol}@{exchange} {quantity} → {adjusted_float} "
                               f"(调整: {adjustment_info['adjustment_percentage']:.3f}%)")
            
            # 缓存结果
            self._cache_result(cache_key, adjustment_info)
            
            return adjustment_info
            
        except Exception as e:
            self.logger.error(f"❌ 数量调整失败: {e}")
            return {
                "original_quantity": quantity,
                "adjusted_quantity": quantity,
                "step_size": step_size,
                "adjustment_amount": 0,
                "adjustment_percentage": 0,
                "exchange": exchange,
                "symbol": symbol,
                "compliant": False,
                "error": str(e),
                "method": "error_fallback"
            }
    
    def format_quantity_for_exchange(self, quantity: float, step_size: float, 
                                   exchange: str, symbol: str) -> str:
        """
        为交易所格式化数量
        返回符合交易所规则的数量字符串
        """
        
        # 调整数量
        adjustment_info = self.adjust_quantity_to_exchange_rules(
            quantity, step_size, exchange, symbol
        )
        
        adjusted_quantity = adjustment_info["adjusted_quantity"]
        
        # 根据步长确定小数位数
        if step_size >= 1:
            decimal_places = 0
        else:
            # 计算步长的小数位数
            step_str = f"{step_size:.10f}".rstrip('0')
            if '.' in step_str:
                decimal_places = len(step_str.split('.')[1])
            else:
                decimal_places = 0
        
        # 格式化为字符串
        if decimal_places == 0:
            formatted = f"{int(adjusted_quantity)}"
        else:
            formatted = f"{adjusted_quantity:.{decimal_places}f}"
        
        # 去除尾随零（但保留至少一位小数如果原本有小数）
        if '.' in formatted:
            formatted = formatted.rstrip('0').rstrip('.')
        
        return formatted
    
    def validate_quantity_compliance(self, quantity: float, step_size: float, 
                                   min_quantity: float = 0) -> Dict[str, Any]:
        """
        验证数量是否符合交易所规则
        """
        
        try:
            # 检查最小数量
            if quantity < min_quantity:
                return {
                    "compliant": False,
                    "reason": "below_minimum",
                    "min_required": min_quantity,
                    "actual": quantity
                }
            
            # 检查步长合规性
            quantity_decimal = Decimal(str(quantity))
            step_decimal = Decimal(str(step_size))
            
            remainder = quantity_decimal % step_decimal
            
            if remainder == 0:
                return {
                    "compliant": True,
                    "reason": "perfect_match"
                }
            else:
                return {
                    "compliant": False,
                    "reason": "step_size_violation",
                    "remainder": float(remainder),
                    "step_size": step_size
                }
                
        except Exception as e:
            return {
                "compliant": False,
                "reason": "validation_error",
                "error": str(e)
            }
    
    def get_exchange_precision_info(self, exchange: str, symbol: str, 
                                  api_precision: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取交易所精度信息的统一接口
        处理不同交易所的API格式差异
        """
        
        try:
            if exchange.lower() == "bybit":
                # Bybit使用basePrecision
                step_size = float(api_precision.get("step_size", 0.000001))
                min_qty = float(api_precision.get("min_qty", 0))
                
            elif exchange.lower() == "gate":
                # Gate使用amount_precision
                amount_precision = api_precision.get("amount_precision", 6)
                step_size = 10 ** (-amount_precision)
                min_qty = float(api_precision.get("min_qty", 0))
                
            elif exchange.lower() == "okx":
                # OKX使用lotSz
                step_size = float(api_precision.get("lotSz", 0.000001))
                min_qty = float(api_precision.get("minSz", 0))
                
            else:
                # 未知交易所，使用默认值
                step_size = 0.000001
                min_qty = 0
            
            return {
                "step_size": step_size,
                "min_quantity": min_qty,
                "exchange": exchange,
                "symbol": symbol,
                "source": "api_normalized"
            }
            
        except Exception as e:
            self.logger.error(f"❌ 精度信息获取失败: {exchange} {symbol} - {e}")
            return {
                "step_size": 0.000001,  # 安全的默认值
                "min_quantity": 0,
                "exchange": exchange,
                "symbol": symbol,
                "source": "error_fallback",
                "error": str(e)
            }
    
    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        if cache_key in self.adjustment_cache:
            cached_data = self.adjustment_cache[cache_key]
            if time.time() - cached_data["timestamp"] < self.cache_ttl:
                return cached_data["result"]
            else:
                # 缓存过期，删除
                del self.adjustment_cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """缓存结果"""
        self.adjustment_cache[cache_key] = {
            "result": result,
            "timestamp": time.time()
        }
        
        # 简单的缓存清理：如果缓存太大，清理一半
        if len(self.adjustment_cache) > 1000:
            keys_to_remove = list(self.adjustment_cache.keys())[:500]
            for key in keys_to_remove:
                del self.adjustment_cache[key]

# 全局实例
_correct_precision_solution = None

def get_correct_precision_solution() -> CorrectPrecisionSolution:
    """获取正确精度解决方案实例"""
    global _correct_precision_solution
    if _correct_precision_solution is None:
        _correct_precision_solution = CorrectPrecisionSolution()
    return _correct_precision_solution
