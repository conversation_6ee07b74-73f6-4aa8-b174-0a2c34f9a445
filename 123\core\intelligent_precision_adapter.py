#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能精度适配器
解决API精度与实际交易需求不匹配的问题
这是通用解决方案，支持任意代币
"""

import logging
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Any, Optional
import math

logger = logging.getLogger(__name__)

class IntelligentPrecisionAdapter:
    """
    智能精度适配器
    
    核心功能：
    1. 检测实际交易数量的精度需求
    2. 对比API返回的精度限制
    3. 智能选择最优精度策略
    4. 提供精度不匹配的解决方案
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 精度适配策略
        self.adaptation_strategies = {
            "upgrade": "升级精度到满足需求",
            "round": "按API精度四舍五入",
            "split": "拆分订单满足精度要求",
            "fallback": "使用兜底高精度"
        }
    
    def analyze_precision_requirement(self, quantity: float) -> Dict[str, Any]:
        """分析数量的精度需求"""
        quantity_str = str(quantity)
        
        if '.' not in quantity_str:
            return {
                "decimal_places": 0,
                "required_step_size": 1.0,
                "precision_level": "integer"
            }
        
        decimal_part = quantity_str.split('.')[1]
        decimal_places = len(decimal_part.rstrip('0'))  # 去除尾随零
        required_step_size = 10 ** (-decimal_places)
        
        # 精度等级分类
        if decimal_places <= 2:
            precision_level = "low"
        elif decimal_places <= 4:
            precision_level = "medium"
        elif decimal_places <= 6:
            precision_level = "high"
        else:
            precision_level = "ultra_high"
        
        return {
            "decimal_places": decimal_places,
            "required_step_size": required_step_size,
            "precision_level": precision_level,
            "original_quantity": quantity
        }
    
    def check_precision_compatibility(self, api_step_size: float, required_step_size: float) -> Dict[str, Any]:
        """检查API精度与需求精度的兼容性"""
        
        # 计算精度比率
        precision_ratio = api_step_size / required_step_size
        
        if precision_ratio <= 1.0:
            # API精度足够或更高
            compatibility = "sufficient"
            issue_level = "none"
        elif precision_ratio <= 10:
            # API精度略低，可以接受
            compatibility = "acceptable"
            issue_level = "minor"
        elif precision_ratio <= 100:
            # API精度明显不足
            compatibility = "insufficient"
            issue_level = "major"
        else:
            # API精度严重不足
            compatibility = "critical"
            issue_level = "critical"
        
        return {
            "api_step_size": api_step_size,
            "required_step_size": required_step_size,
            "precision_ratio": precision_ratio,
            "compatibility": compatibility,
            "issue_level": issue_level
        }
    
    def select_adaptation_strategy(self, compatibility_info: Dict[str, Any], 
                                 exchange_name: str, symbol: str) -> str:
        """选择最优的精度适配策略"""
        
        compatibility = compatibility_info["compatibility"]
        issue_level = compatibility_info["issue_level"]
        
        if compatibility == "sufficient":
            return "use_api"  # 直接使用API精度
        
        elif compatibility == "acceptable":
            return "round"  # 按API精度四舍五入
        
        elif compatibility == "insufficient":
            # 根据交易所特性选择策略
            if exchange_name.lower() == "bybit":
                # Bybit支持更高精度，升级精度
                return "upgrade"
            else:
                # 其他交易所使用兜底策略
                return "fallback"
        
        else:  # critical
            # 严重不匹配，使用兜底高精度
            return "fallback"
    
    def apply_precision_adaptation(self, quantity: float, api_precision: Dict[str, Any], 
                                 strategy: str) -> Dict[str, Any]:
        """应用精度适配策略"""
        
        api_step_size = api_precision.get("step_size", 0.001)
        
        if strategy == "use_api":
            # 直接使用API精度
            adapted_step_size = api_step_size
            adapted_precision = self._calculate_precision(api_step_size)
            
        elif strategy == "round":
            # 按API精度四舍五入
            adapted_step_size = api_step_size
            adapted_precision = self._calculate_precision(api_step_size)
            # 数量需要按步长调整
            quantity = self._round_to_step_size(quantity, api_step_size)
            
        elif strategy == "upgrade":
            # 升级精度到满足需求
            requirement = self.analyze_precision_requirement(quantity)
            adapted_step_size = requirement["required_step_size"]
            adapted_precision = requirement["decimal_places"]
            
        elif strategy == "fallback":
            # 使用兜底高精度
            adapted_step_size = 0.000001  # 6位小数精度
            adapted_precision = 6
            
        else:
            # 默认策略
            adapted_step_size = 0.000001
            adapted_precision = 6
        
        return {
            "step_size": adapted_step_size,
            "amount_precision": adapted_precision,
            "adapted_quantity": quantity,
            "strategy_used": strategy,
            "source": f"intelligent_adaptation_{strategy}"
        }
    
    def _calculate_precision(self, step_size: float) -> int:
        """根据步长计算精度位数"""
        if step_size >= 1:
            return 0
        
        # 转换为字符串并计算小数位数
        step_str = f"{step_size:.10f}".rstrip('0')
        if '.' in step_str:
            return len(step_str.split('.')[1])
        return 0
    
    def _round_to_step_size(self, quantity: float, step_size: float) -> float:
        """按步长四舍五入数量"""
        if step_size <= 0:
            return quantity
        
        # 使用Decimal确保精度
        decimal_quantity = Decimal(str(quantity))
        decimal_step = Decimal(str(step_size))
        
        # 计算最接近的步长倍数
        steps = (decimal_quantity / decimal_step).quantize(Decimal('1'), rounding=ROUND_DOWN)
        rounded_quantity = float(steps * decimal_step)
        
        return rounded_quantity
    
    def get_optimal_precision(self, quantity: float, api_precision: Dict[str, Any], 
                            exchange_name: str, symbol: str) -> Dict[str, Any]:
        """获取最优精度配置（主要接口）"""
        
        # 1. 分析精度需求
        requirement = self.analyze_precision_requirement(quantity)
        self.logger.debug(f"精度需求分析: {symbol} 数量={quantity} 需求={requirement}")
        
        # 2. 检查兼容性
        api_step_size = api_precision.get("step_size", 0.001)
        compatibility = self.check_precision_compatibility(
            api_step_size, requirement["required_step_size"]
        )
        self.logger.debug(f"精度兼容性: {compatibility}")
        
        # 3. 选择适配策略
        strategy = self.select_adaptation_strategy(compatibility, exchange_name, symbol)
        self.logger.info(f"选择适配策略: {strategy} (兼容性: {compatibility['compatibility']})")
        
        # 4. 应用适配策略
        adapted_precision = self.apply_precision_adaptation(quantity, api_precision, strategy)
        
        # 5. 记录适配过程
        adaptation_log = {
            "symbol": symbol,
            "exchange": exchange_name,
            "original_quantity": quantity,
            "api_step_size": api_step_size,
            "required_step_size": requirement["required_step_size"],
            "compatibility": compatibility["compatibility"],
            "strategy": strategy,
            "final_step_size": adapted_precision["step_size"],
            "final_precision": adapted_precision["amount_precision"]
        }
        
        self.logger.info(f"智能精度适配完成: {adaptation_log}")
        
        return adapted_precision

# 全局实例
_precision_adapter = None

def get_intelligent_precision_adapter() -> IntelligentPrecisionAdapter:
    """获取智能精度适配器实例"""
    global _precision_adapter
    if _precision_adapter is None:
        _precision_adapter = IntelligentPrecisionAdapter()
    return _precision_adapter
