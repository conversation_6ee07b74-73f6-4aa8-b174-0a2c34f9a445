#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精准诊断脚本：Bybit数量精度错误问题
分析execution_engine.log中的170137错误
"""

import os
import sys
import re
import json
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def analyze_log_errors():
    """分析日志中的精度错误"""
    log_file = "logs/execution_engine.log"

    print("🔍 分析execution_engine.log中的精度错误...")

    if not os.path.exists(log_file):
        print(f"❌ 日志文件不存在: {log_file}")
        return
    
    errors = []
    with open(log_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for i, line in enumerate(lines, 1):
        if "170137" in line and "too many decimals" in line:
            # 提取关键信息
            quantity_match = re.search(r"quantity='([^']+)'", line)
            step_size_match = re.search(r"step_size='([^']+)'", line)
            
            if quantity_match and step_size_match:
                quantity = quantity_match.group(1)
                step_size = step_size_match.group(1)
                
                errors.append({
                    'line': i,
                    'quantity': quantity,
                    'step_size': step_size,
                    'quantity_decimals': len(quantity.split('.')[-1]) if '.' in quantity else 0,
                    'step_size_decimals': len(step_size.split('.')[-1]) if '.' in step_size else 0
                })
    
    print(f"📊 发现 {len(errors)} 个精度错误:")
    for error in errors:
        print(f"   行 {error['line']}: quantity={error['quantity']} ({error['quantity_decimals']}位小数), step_size={error['step_size']} ({error['step_size_decimals']}位小数)")
    
    return errors

def check_trading_rules():
    """检查交易规则配置"""
    print("\n🔍 检查RESOLV-USDT的交易规则配置...")
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 检查Bybit现货规则
        rule = preloader.get_trading_rule("RESOLV-USDT", "bybit", "spot")
        
        if rule:
            print(f"✅ 找到交易规则: RESOLV-USDT_bybit_spot")
            print(f"   qty_step: {rule.get('qty_step', 'N/A')}")
            print(f"   price_step: {rule.get('price_step', 'N/A')}")
            print(f"   min_order_qty: {rule.get('min_order_qty', 'N/A')}")
            print(f"   max_order_qty: {rule.get('max_order_qty', 'N/A')}")
            return rule
        else:
            print(f"❌ 未找到交易规则: RESOLV-USDT_bybit_spot")

            # 列出所有可用的规则
            all_rules = preloader.get_all_trading_rules()
            resolv_rules = [k for k in all_rules.keys() if 'RESOLV' in k]
            print(f"📋 RESOLV相关规则: {resolv_rules}")
            
    except Exception as e:
        print(f"❌ 检查交易规则失败: {e}")
    
    return None

def test_quantity_precision():
    """测试数量精度处理"""
    print("\n🔍 测试数量精度处理逻辑...")
    
    test_cases = [
        {'quantity': 173.01038062283735, 'step_size': '0.001'},
        {'quantity': 172.58382642998026, 'step_size': '0.001'},
        {'quantity': 173.01, 'step_size': '0.001'},
        {'quantity': 172.583, 'step_size': '0.001'},
    ]
    
    for case in test_cases:
        quantity = case['quantity']
        step_size = case['step_size']
        
        # 模拟精度处理
        step_decimal = Decimal(step_size)
        quantity_decimal = Decimal(str(quantity))
        
        # 计算应该的精度位数
        step_precision = len(step_size.split('.')[-1]) if '.' in step_size else 0
        
        # 截取到正确精度
        corrected_quantity = quantity_decimal.quantize(step_decimal, rounding=ROUND_DOWN)
        
        print(f"   原始数量: {quantity}")
        print(f"   step_size: {step_size} ({step_precision}位小数)")
        print(f"   修正数量: {corrected_quantity}")
        print(f"   是否符合精度: {len(str(corrected_quantity).split('.')[-1]) <= step_precision}")
        print()

def check_bybit_exchange_precision():
    """检查Bybit交易所的精度处理"""
    print("\n🔍 检查Bybit交易所的精度处理逻辑...")
    
    try:
        from exchanges.bybit_exchange import BybitExchange
        
        # 检查是否有精度处理方法
        methods = [method for method in dir(BybitExchange) if 'precision' in method.lower() or 'format' in method.lower()]
        print(f"📋 Bybit精度相关方法: {methods}")
        
        # 检查订单创建方法
        if hasattr(BybitExchange, 'create_spot_order'):
            print("✅ 找到create_spot_order方法")
        else:
            print("❌ 未找到create_spot_order方法")
            
    except Exception as e:
        print(f"❌ 检查Bybit交易所失败: {e}")

def main():
    """主诊断函数"""
    print("🚀 开始精准诊断：Bybit数量精度错误问题")
    print("=" * 60)
    
    # 1. 分析日志错误
    errors = analyze_log_errors()
    
    # 2. 检查交易规则
    trading_rule = check_trading_rules()
    
    # 3. 测试精度处理
    test_quantity_precision()
    
    # 4. 检查交易所精度处理
    check_bybit_exchange_precision()
    
    print("\n📊 诊断总结:")
    print("=" * 60)
    
    if errors:
        print(f"🚨 发现 {len(errors)} 个精度错误")
        print("🔍 问题分析:")
        print("   1. 数量精度超过step_size允许的精度")
        print("   2. 需要在发送订单前进行精度截取")
        print("   3. 可能是智能协调后的精度处理有问题")
    else:
        print("✅ 未发现明显的精度错误")
    
    print("\n🎯 建议修复方案:")
    print("   1. 在SpotTrader中添加精度截取逻辑")
    print("   2. 确保数量格式化符合交易所要求")
    print("   3. 统一精度处理，避免重复造轮子")

if __name__ == "__main__":
    main()
