#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度诊断脚本：Bybit精度处理问题
专门诊断RESOLV-USDT在Bybit现货的精度处理问题
"""

import os
import sys
import asyncio
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def diagnose_trading_rules():
    """诊断交易规则获取问题"""
    print("🔍 诊断RESOLV-USDT交易规则获取问题...")
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 检查预加载状态
        print(f"📊 预加载状态:")
        print(f"   预加载完成: {preloader.preload_completed}")
        print(f"   正在预加载: {preloader.is_preloading}")
        print(f"   缓存规则数: {len(preloader.trading_rules)}")
        
        # 尝试获取RESOLV-USDT的交易规则
        symbol = "RESOLV-USDT"
        exchange = "bybit"
        market_type = "spot"
        
        print(f"\n🔍 尝试获取交易规则: {symbol} {exchange} {market_type}")
        
        # 方法1：直接获取
        rule = preloader.get_trading_rule(exchange, symbol, market_type)
        if rule:
            print(f"✅ 找到交易规则:")
            print(f"   qty_step: {rule.qty_step}")
            print(f"   qty_precision: {rule.qty_precision}")
            print(f"   min_qty: {rule.min_qty}")
            print(f"   max_qty: {rule.max_qty}")
        else:
            print(f"❌ 未找到交易规则")
            
            # 检查缓存中的所有规则
            print(f"\n📋 缓存中的所有规则:")
            for key in preloader.trading_rules.keys():
                if "RESOLV" in key or "bybit" in key:
                    print(f"   {key}")
        
        # 方法2：测试精度格式化
        test_amounts = [173.01038062283735, 172.58382642998026, 173.01, 172.583]
        
        print(f"\n🔍 测试精度格式化:")
        for amount in test_amounts:
            try:
                formatted = preloader.format_amount_unified(amount, exchange, symbol, market_type)
                print(f"   {amount} -> '{formatted}'")
            except Exception as e:
                print(f"   {amount} -> ERROR: {e}")
        
        return rule
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_precision_calculation():
    """测试精度计算逻辑"""
    print("\n🔍 测试精度计算逻辑...")
    
    # 模拟step_size = 0.001的情况
    step_size = 0.001
    test_amounts = [173.01038062283735, 172.58382642998026, 173.01, 172.583]
    
    print(f"📊 step_size = {step_size}")
    
    for amount in test_amounts:
        # 使用Decimal进行精确计算
        amount_decimal = Decimal(str(amount))
        step_decimal = Decimal(str(step_size))
        
        # 截取到步长的整数倍
        adjusted = (amount_decimal // step_decimal) * step_decimal
        
        # 计算精度位数
        precision = len(str(step_size).split('.')[-1]) if '.' in str(step_size) else 0
        
        # 格式化
        formatted = f"{float(adjusted):.{precision}f}"
        
        print(f"   {amount} -> {adjusted} -> '{formatted}' (精度={precision})")
        
        # 验证是否符合步长要求
        remainder = adjusted % step_decimal
        is_valid = remainder == 0
        print(f"     步长验证: {remainder} == 0 ? {is_valid}")

async def diagnose_bybit_api_call():
    """诊断Bybit API调用的精度问题"""
    print("\n🔍 诊断Bybit API调用精度问题...")
    
    # 模拟日志中的错误情况
    test_cases = [
        {"quantity": "173.01", "step_size": "0.001"},
        {"quantity": "172.583", "step_size": "0.001"}
    ]
    
    for case in test_cases:
        quantity_str = case["quantity"]
        step_size_str = case["step_size"]
        
        print(f"\n📊 测试案例: quantity='{quantity_str}', step_size='{step_size_str}'")
        
        # 检查字符串格式
        quantity_decimals = len(quantity_str.split('.')[-1]) if '.' in quantity_str else 0
        step_size_decimals = len(step_size_str.split('.')[-1]) if '.' in step_size_str else 0
        
        print(f"   quantity小数位数: {quantity_decimals}")
        print(f"   step_size小数位数: {step_size_decimals}")
        print(f"   精度是否匹配: {quantity_decimals <= step_size_decimals}")
        
        # 检查是否是步长的整数倍
        quantity_decimal = Decimal(quantity_str)
        step_decimal = Decimal(step_size_str)
        remainder = quantity_decimal % step_decimal
        
        print(f"   步长余数: {remainder}")
        print(f"   是否是步长整数倍: {remainder == 0}")
        
        # 如果不是整数倍，计算正确的值
        if remainder != 0:
            corrected = (quantity_decimal // step_decimal) * step_decimal
            corrected_str = f"{float(corrected):.{step_size_decimals}f}"
            print(f"   修正后的值: {corrected_str}")

async def main():
    """主诊断函数"""
    print("🚀 开始深度诊断：Bybit精度处理问题")
    print("=" * 60)
    
    # 1. 诊断交易规则获取
    rule = await diagnose_trading_rules()
    
    # 2. 测试精度计算
    await test_precision_calculation()
    
    # 3. 诊断API调用精度问题
    await diagnose_bybit_api_call()
    
    print("\n📊 诊断总结:")
    print("=" * 60)
    
    if rule:
        print("✅ 交易规则获取正常")
        print(f"   步长: {rule.qty_step}")
        print(f"   精度: {rule.qty_precision}")
    else:
        print("❌ 交易规则获取失败")
        print("   可能原因:")
        print("   1. 预加载未完成")
        print("   2. 缓存键不匹配")
        print("   3. 交易规则未正确加载")
    
    print("\n🎯 问题分析:")
    print("   从日志看，quantity='173.01'和'172.583'都是3位小数")
    print("   step_size='0.001'也是3位小数")
    print("   理论上应该符合精度要求")
    print("   但Bybit API仍然报错'Order quantity has too many decimals'")
    print("   可能的原因:")
    print("   1. 数量不是step_size的整数倍")
    print("   2. Bybit API对精度有特殊要求")
    print("   3. 尾随零处理问题")
    
    print("\n🎯 建议修复方案:")
    print("   1. 确保数量是step_size的精确整数倍")
    print("   2. 应用Bybit尾随零修复")
    print("   3. 添加防御性精度验证")
    print("   4. 在发送API前进行最终精度检查")

if __name__ == "__main__":
    asyncio.run(main())
