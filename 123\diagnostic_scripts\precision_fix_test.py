#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精度修复验证脚本
验证RESOLV-USDT在Bybit现货的精度处理修复效果
"""

import os
import sys
import asyncio
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def test_precision_fix():
    """测试精度修复效果"""
    print("🚀 开始测试精度修复效果")
    print("=" * 60)
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 测试参数
        symbol = "RESOLV-USDT"
        exchange = "bybit"
        market_type = "spot"
        
        # 从日志中提取的问题数量
        test_amounts = [
            173.01038062283735,  # 原始计算数量
            172.58382642998026,  # 原始计算数量
            173.01,              # 日志中的错误数量
            172.583              # 日志中的错误数量
        ]
        
        print(f"🔍 测试交易对: {symbol} {exchange} {market_type}")
        
        # 1. 检查交易规则
        rule = preloader.get_trading_rule(exchange, symbol, market_type)
        if rule:
            print(f"✅ 交易规则获取成功:")
            print(f"   qty_step: {rule.qty_step}")
            print(f"   qty_precision: {rule.qty_precision}")
            print(f"   min_qty: {rule.min_qty}")
            print(f"   max_qty: {rule.max_qty}")
        else:
            print(f"❌ 交易规则获取失败")
            return False
        
        print(f"\n🔍 测试精度格式化修复:")
        all_passed = True
        
        for i, amount in enumerate(test_amounts, 1):
            print(f"\n📊 测试案例 {i}: {amount}")
            
            try:
                # 使用修复后的格式化方法
                formatted = preloader.format_amount_unified(amount, exchange, symbol, market_type)
                print(f"   格式化结果: '{formatted}'")
                
                # 验证格式化结果
                formatted_float = float(formatted)
                formatted_str = str(formatted_float)
                
                # 检查小数位数
                if '.' in formatted_str:
                    decimal_places = len(formatted_str.split('.')[1])
                    print(f"   小数位数: {decimal_places}")
                    
                    if decimal_places <= 6:
                        print(f"   ✅ 精度检查通过 (≤6位小数)")
                    else:
                        print(f"   ❌ 精度检查失败 (>6位小数)")
                        all_passed = False
                else:
                    print(f"   ✅ 整数，无小数位")
                
                # 验证是否是步长的整数倍
                amount_decimal = Decimal(formatted)
                step_decimal = Decimal(str(rule.qty_step))
                remainder = amount_decimal % step_decimal
                
                if remainder == 0:
                    print(f"   ✅ 步长验证通过 (余数={remainder})")
                else:
                    print(f"   ❌ 步长验证失败 (余数={remainder})")
                    all_passed = False
                
                # 模拟Bybit API调用验证
                print(f"   🔧 模拟API参数: qty='{formatted}'")
                
            except Exception as e:
                print(f"   ❌ 格式化失败: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_bybit_exchange_precision():
    """测试Bybit交易所的防御性精度验证"""
    print(f"\n🔍 测试防御性精度验证逻辑:")
    print("=" * 60)

    try:
        from decimal import Decimal, ROUND_DOWN

        # 测试数量
        test_amounts = [173.01038062283735, 172.58382642998026]

        print(f"📊 测试防御性精度验证逻辑:")

        for amount in test_amounts:
            print(f"\n🔧 测试数量: {amount}")

            try:
                # 模拟防御性精度验证逻辑
                formatted_float = float(amount)
                formatted_str = str(formatted_float)

                # 检查小数位数是否过多
                if '.' in formatted_str:
                    decimal_places = len(formatted_str.split('.')[1])
                    print(f"   原始小数位数: {decimal_places}")

                    if decimal_places > 6:  # Bybit通常支持最多6位小数
                        # 强制截取到6位小数
                        amount_decimal = Decimal(str(amount))
                        truncated = amount_decimal.quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
                        formatted_amount = str(truncated)
                        print(f"   ⚠️ 防御性截取: {formatted_float} -> {formatted_amount}")
                    else:
                        formatted_amount = str(formatted_float)
                        print(f"   ✅ 精度验证通过: {formatted_amount}")
                else:
                    formatted_amount = str(formatted_float)
                    print(f"   ✅ 整数格式: {formatted_amount}")

                # 验证最终结果
                final_float = float(formatted_amount)
                final_str = str(final_float)
                if '.' in final_str:
                    final_decimal_places = len(final_str.split('.')[1])
                    if final_decimal_places <= 6:
                        print(f"   ✅ 最终验证通过 ({final_decimal_places}位小数)")
                    else:
                        print(f"   ❌ 最终验证失败 ({final_decimal_places}位小数)")
                else:
                    print(f"   ✅ 最终结果为整数")

            except Exception as e:
                print(f"   ❌ 防御性验证异常: {e}")

        return True

    except Exception as e:
        print(f"❌ 防御性验证测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始精度修复验证测试")
    print("=" * 80)
    
    # 1. 测试精度修复
    precision_test_passed = await test_precision_fix()
    
    # 2. 测试防御性精度验证逻辑
    bybit_test_passed = await test_bybit_exchange_precision()
    
    print(f"\n📊 测试总结:")
    print("=" * 80)
    
    if precision_test_passed:
        print("✅ 精度修复测试通过")
    else:
        print("❌ 精度修复测试失败")
    
    if bybit_test_passed:
        print("✅ 防御性精度验证逻辑测试通过")
    else:
        print("❌ 防御性精度验证逻辑测试失败")
    
    overall_success = precision_test_passed and bybit_test_passed
    
    if overall_success:
        print("\n🎉 所有测试通过！修复成功！")
        print("🎯 修复要点:")
        print("   1. 兜底精度从0.001提升到0.000001")
        print("   2. 精度位数从3位提升到6位")
        print("   3. 添加了防御性精度验证")
        print("   4. 确保数量是步长的整数倍")
        print("   5. 应用了Bybit尾随零修复")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
    
    return overall_success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
