#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API失效根本原因诊断
找出为什么API调用失败，导致使用了0.001的默认值
"""

import os
import sys
import asyncio
import json
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def trace_precision_flow():
    """追踪精度获取的完整流程"""
    print("🔍 追踪RESOLV-USDT精度获取的完整流程")
    print("=" * 80)
    
    symbol = "RESOLV-USDT"
    market_type = "spot"
    exchange_name = "bybit"
    
    try:
        # 1. 创建交易所实例
        print("📋 步骤1: 创建Bybit交易所实例")
        from exchanges.bybit_exchange import BybitExchange
        
        bybit_api_key = os.getenv("BYBIT_API_KEY", "")
        bybit_api_secret = os.getenv("BYBIT_API_SECRET", "")
        
        if not bybit_api_key or not bybit_api_secret:
            print("   ❌ Bybit API密钥缺失")
            return False
        
        exchange = BybitExchange(bybit_api_key, bybit_api_secret)
        print("   ✅ Bybit交易所实例创建成功")
        
        # 2. 获取交易规则预加载器
        print("\n📋 步骤2: 获取交易规则预加载器")
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        print("   ✅ 交易规则预加载器获取成功")
        
        # 3. 检查缓存状态
        print(f"\n📋 步骤3: 检查缓存状态")
        cache_key = f"{exchange_name}_{symbol}_{market_type}"
        
        if hasattr(preloader, 'precision_cache') and cache_key in preloader.precision_cache:
            cached_data = preloader.precision_cache[cache_key]
            print(f"   ✅ 找到精度缓存: {cache_key}")
            print(f"      数据: {cached_data}")
            
            # 检查缓存是否过期
            import time
            current_time = time.time()
            cache_time = cached_data.get('cache_time', 0)
            ttl = 3600  # 1小时TTL
            
            if current_time - cache_time > ttl:
                print(f"   ⚠️ 缓存已过期: {current_time - cache_time:.1f}s > {ttl}s")
            else:
                print(f"   ✅ 缓存有效: {current_time - cache_time:.1f}s < {ttl}s")
                return cached_data.get('data')
        else:
            print(f"   ❌ 未找到精度缓存: {cache_key}")
        
        # 4. 直接调用API精度获取
        print(f"\n📋 步骤4: 直接调用API精度获取")
        try:
            precision_info = await preloader._get_precision_from_exchange_api(
                exchange, symbol, market_type
            )
            
            if precision_info:
                print(f"   ✅ API精度获取成功:")
                print(f"      step_size: {precision_info.get('step_size')}")
                print(f"      amount_precision: {precision_info.get('amount_precision')}")
                print(f"      source: {precision_info.get('source')}")
                return precision_info
            else:
                print(f"   ❌ API精度获取失败: 返回None")
                
        except Exception as e:
            print(f"   ❌ API精度获取异常: {e}")
            import traceback
            print(f"      详细错误: {traceback.format_exc()}")
        
        # 5. 检查智能默认值
        print(f"\n📋 步骤5: 检查智能默认值")
        try:
            default_info = preloader._get_intelligent_default_precision(exchange_name, symbol, market_type)
            print(f"   智能默认值: {default_info}")
            return default_info
        except Exception as e:
            print(f"   ❌ 智能默认值获取失败: {e}")
        
        # 6. 检查最终兜底值
        print(f"\n📋 步骤6: 检查最终兜底值")
        try:
            fallback_info = {
                "step_size": 0.000001,  # 应该是修复后的值
                "min_amount": 0.000001,
                "max_amount": 1000000,
                "price_precision": 4,
                "amount_precision": 6,
                "min_notional": 1.0,
                "source": "fallback"
            }
            print(f"   最终兜底值: {fallback_info}")
            return fallback_info
        except Exception as e:
            print(f"   ❌ 最终兜底值获取失败: {e}")
        
        return None
        
    except Exception as e:
        print(f"❌ 精度流程追踪失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_format_amount_flow():
    """测试格式化数量的完整流程"""
    print(f"\n🔧 测试格式化数量的完整流程")
    print("=" * 80)
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 测试数据
        test_amount = 173.01038062283735
        exchange_name = "bybit"
        symbol = "RESOLV-USDT"
        market_type = "spot"
        
        print(f"📋 测试参数:")
        print(f"   数量: {test_amount}")
        print(f"   交易所: {exchange_name}")
        print(f"   交易对: {symbol}")
        print(f"   市场类型: {market_type}")
        
        # 调用格式化方法
        formatted = preloader.format_amount_unified(test_amount, exchange_name, symbol, market_type)
        print(f"\n📊 格式化结果: {test_amount} -> '{formatted}'")
        
        # 分析格式化结果
        if '.' in formatted:
            decimal_places = len(formatted.split('.')[1])
            print(f"   小数位数: {decimal_places}")
            
            if decimal_places <= 3:
                print(f"   ⚠️ 疑似使用了0.001精度 (≤3位小数)")
            elif decimal_places >= 5:
                print(f"   ✅ 使用了高精度 (≥5位小数)")
            else:
                print(f"   🤔 中等精度 (4位小数)")
        
        return formatted
        
    except Exception as e:
        print(f"❌ 格式化流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def check_trading_rules_cache():
    """检查交易规则缓存状态"""
    print(f"\n📋 检查交易规则缓存状态")
    print("=" * 80)
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        symbol = "RESOLV-USDT"
        market_type = "spot"
        exchange_name = "bybit"
        
        # 检查交易规则缓存
        rule_key = f"{exchange_name}_{symbol}_{market_type}"
        
        if hasattr(preloader, 'trading_rules') and rule_key in preloader.trading_rules:
            rule = preloader.trading_rules[rule_key]
            print(f"   ✅ 找到交易规则: {rule_key}")
            print(f"      qty_step: {rule.qty_step}")
            print(f"      qty_precision: {rule.qty_precision}")
            print(f"      timestamp: {rule.timestamp}")
            
            # 检查规则是否过期
            import time
            current_time = time.time()
            rule_age = current_time - rule.timestamp
            print(f"      规则年龄: {rule_age:.1f}秒")
            
            return rule
        else:
            print(f"   ❌ 未找到交易规则: {rule_key}")
            
            # 显示所有相关的规则键
            if hasattr(preloader, 'trading_rules'):
                print(f"   📋 现有规则键:")
                for key in preloader.trading_rules.keys():
                    if "RESOLV" in key or "bybit" in key:
                        print(f"      {key}")
            
            return None
            
    except Exception as e:
        print(f"❌ 交易规则缓存检查失败: {e}")
        return None

async def simulate_opening_order_params():
    """模拟OpeningOrderParams的创建过程"""
    print(f"\n🎯 模拟OpeningOrderParams的创建过程")
    print("=" * 80)
    
    try:
        # 模拟UnifiedOpeningManager的逻辑
        from core.unified_opening_manager import OpeningOrderParams
        
        # 测试数据
        symbol = "RESOLV-USDT"
        quantity = 173.01038062283735
        exchange_name = "bybit"
        market_type = "spot"
        
        print(f"📋 模拟参数:")
        print(f"   symbol: {symbol}")
        print(f"   quantity: {quantity}")
        print(f"   exchange_name: {exchange_name}")
        print(f"   market_type: {market_type}")
        
        # 获取交易规则
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 获取或创建交易规则
        rule_key = f"{exchange_name}_{symbol}_{market_type}"
        
        if hasattr(preloader, 'trading_rules') and rule_key in preloader.trading_rules:
            rule = preloader.trading_rules[rule_key]
            print(f"   ✅ 使用缓存规则: qty_step={rule.qty_step}")
        else:
            print(f"   ⚠️ 规则缓存未命中，需要获取精度信息")
            
            # 模拟精度获取流程
            precision_info = await trace_precision_flow()
            if precision_info:
                step_size = precision_info.get('step_size', 0.001)
                print(f"   📊 获取到精度信息: step_size={step_size}")
            else:
                step_size = 0.001  # 默认值
                print(f"   ⚠️ 使用默认精度: step_size={step_size}")
        
        # 格式化数量
        formatted_quantity = preloader.format_amount_unified(quantity, exchange_name, symbol, market_type)
        print(f"   📊 格式化数量: {quantity} -> '{formatted_quantity}'")
        
        # 创建OpeningOrderParams (模拟)
        if hasattr(preloader, 'trading_rules') and rule_key in preloader.trading_rules:
            rule = preloader.trading_rules[rule_key]
            step_size_str = str(rule.qty_step)
        else:
            step_size_str = "0.001"  # 这里可能是问题所在
        
        print(f"   📊 OpeningOrderParams中的step_size: '{step_size_str}'")
        
        # 检查是否匹配错误日志
        if step_size_str == "0.001":
            print(f"   ❌ 匹配错误日志中的step_size='0.001'")
            print(f"   🎯 这就是问题的根源！")
        else:
            print(f"   ✅ step_size正常")
        
        return step_size_str
        
    except Exception as e:
        print(f"❌ OpeningOrderParams模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """主诊断函数"""
    print("🚀 API失效根本原因深度诊断")
    print("=" * 100)
    
    # 1. 追踪精度获取流程
    precision_info = await trace_precision_flow()
    
    # 2. 测试格式化流程
    formatted_result = await test_format_amount_flow()
    
    # 3. 检查交易规则缓存
    trading_rule = await check_trading_rules_cache()
    
    # 4. 模拟OpeningOrderParams创建
    step_size_result = await simulate_opening_order_params()
    
    print(f"\n📊 诊断总结")
    print("=" * 100)
    
    print(f"🎯 关键发现:")
    print(f"1. API精度获取: {'成功' if precision_info else '失败'}")
    print(f"2. 格式化结果: {formatted_result}")
    print(f"3. 交易规则缓存: {'存在' if trading_rule else '缺失'}")
    print(f"4. OpeningOrderParams step_size: {step_size_result}")
    
    if step_size_result == "0.001":
        print(f"\n🚨 根本原因确认:")
        print(f"   OpeningOrderParams中使用了0.001的step_size")
        print(f"   这与错误日志中的step_size='0.001'完全匹配")
        print(f"   问题出现在交易规则获取或缓存环节")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
